- name: Git Update
  hosts: SWARM
  vars:
    git_repo_path: /home/<USER>/vSWARM/RobotComputingBoard
  tasks:
    - name: Ensure git is installed
      apt:
        name: git
        state: present
      become: yes

    - name: 切换到 main 分支
      command: git checkout main
      args:
        chdir: "{{ git_repo_path }}"

    - name: 拉取最新代码
      command: git pull origin main
      args:
        chdir: "{{ git_repo_path }}"
