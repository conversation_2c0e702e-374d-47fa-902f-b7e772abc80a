#pragma once

#define VICONDATASTREAMSDK_CPP_VERSION_REVISION_CONTROL_SYSTEM "unknown"
#define VICONDATASTREAMSDK_CPP_VERSION_REVISION 0
#define VICONDATASTREAMSDK_CPP_VERSION_MONOTONIC_REVISION 0
#define VICONDATASTREAMSDK_CPP_VERSION_PUBLIC_REVISION_STRING "0"
#define VICONDATASTREAMSDK_CPP_VERSION_BRANCH "unknown"
#define VICONDATASTREAMSDK_CPP_VERSION_REPOSITORY "unknown"
#define VICONDATASTREAMSDK_CPP_VERSION_CHANGESET "0"
#define VICONDATASTREAMSDK_CPP_VERSION_LOCAL_CHANGESET "0"
#define VICONDATASTREAMSDK_CPP_VERSION_LOCAL_BRANCH "unknown"

#define VICONDATASTREAMSDK_CPP_VERSION_MAJOR 1
#define VICONDATASTREAMSDK_CPP_VERSION_MINOR 11
#define VICONDATASTREAMSDK_CPP_VERSION_POINT 0
#define VICONDATASTREAMSDK_CPP_VERSION_RELEASE ""
#define VICONDATASTREAMSDK_CPP_VERSION_SUPPLEMENTAL ""

#define VICONDATASTREAMSDK_CPP_FULL_VERSION_STRING "1.11.0.0"
#define VICONDATASTREAMSDK_CPP_VERSION_STRING "1.11"
#define VICONDATASTREAMSDK_CPP_COMPANY "Vicon Motion Systems Ltd"
#define VICONDATASTREAMSDK_CPP_COPYRIGHT "Copyright \x00A9 2020 Vicon Motion Systems Ltd. All rights reserved."
#define VICONDATASTREAMSDK_CPP_TRADEMARK "Vicon\x00AE is a registered trademark of OMG Plc."
#define VICONDATASTREAMSDK_CPP_PROJECT_NAME "ViconDataStreamSDK_CPP"

#define VICONDATASTREAMSDK_CPP_FULL_VERSION_STRING_L L"1.11.0.0"
#define VICONDATASTREAMSDK_CPP_VERSION_STRING_L L"1.11"
#define VICONDATASTREAMSDK_CPP_COMPANY_L L"Vicon Motion Systems Ltd"
#define VICONDATASTREAMSDK_CPP_COPYRIGHT_L L"Copyright \x00A9 2020 Vicon Motion Systems Ltd. All rights reserved."
#define VICONDATASTREAMSDK_CPP_TRADEMARK_L L"Vicon\x00AE is a registered trademark of OMG Plc."
#define VICONDATASTREAMSDK_CPP_PROJECT_NAME_L L"ViconDataStreamSDK_CPP"
