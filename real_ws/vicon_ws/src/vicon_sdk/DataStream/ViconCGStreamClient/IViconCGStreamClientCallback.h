
//////////////////////////////////////////////////////////////////////////////////
// MIT License
//
// Copyright (c) 2017 Vicon Motion Systems Ltd
//
// Permission is hereby granted, free of charge, to any person obtaining a copy
// of this software and associated documentation files (the "Software"), to deal
// in the Software without restriction, including without limitation the rights
// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
// copies of the Software, and to permit persons to whom the Software is
// furnished to do so, subject to the following conditions:
//
// The above copyright notice and this permission notice shall be included in all
// copies or substantial portions of the Software.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
// SOFTWARE.
//////////////////////////////////////////////////////////////////////////////////
#pragma once

#include <memory>

class VStaticObjects;
class VDynamicObjects;

class IViconCGStreamClientCallback
{
public:
  virtual void OnConnect();
  virtual void OnStaticObjects( std::shared_ptr< const VStaticObjects > i_pStaticObjects );
  virtual void OnDynamicObjects( std::shared_ptr< const VDynamicObjects > i_pDynamicObjects );
  virtual void OnDisconnect();
  virtual ~IViconCGStreamClientCallback();
};

inline void IViconCGStreamClientCallback::OnConnect()
{
}


inline void IViconCGStreamClientCallback::OnStaticObjects( std::shared_ptr< const VStaticObjects > /*i_pStaticObjects*/ )
{
}


inline void IViconCGStreamClientCallback::OnDynamicObjects( std::shared_ptr< const VDynamicObjects > /*i_pDynamicObjects*/ )
{
}

inline void IViconCGStreamClientCallback::OnDisconnect()
{
}

inline IViconCGStreamClientCallback::~IViconCGStreamClientCallback()
{
}
