# Autogenerated makefile for ViconDataStreamSDK_C

ifndef VERBOSE
.SILENT :
endif
.SUFFIXES :

ifdef CONFIG
ifneq ($(CONFIG), Debug)
ifneq ($(CONFIG), InternalRelease)
ifneq ($(CONFIG), Release)
Error: unknown configuration.
endif
endif
endif
else
CONFIG=Debug
endif

ifeq ($(CONFIG), Debug)
DEFINES=-DTCM_LINUX -DTCM_UNIX -D_DEBUG -D_EXPORTING -DPROJECT_SOURCE_PATH=\".\" -DRELEASE_SOFTWARE 
SYSTEMINCLUDEPATHS=-isystem../../../../thirdparty/Boost/boost-1.58.0-dynamic-linux-x64/installed/include 
INCLUDEPATHS=-I. -I../.. -I.. -I. -IDebug 
LIBRARYPATHS=-L../../../../lib/Debug -L../../../../thirdparty/Boost/boost-1.58.0-dynamic-linux-x64/installed/lib  -L../../../../bin/Debug
LIBRARIES=-lboost_atomic-mt-d -lboost_chrono-mt-d -lboost_container-mt-d -lboost_context-mt-d -lboost_coroutine-mt-d -lboost_date_time-mt-d -lboost_filesystem-mt-d -lboost_graph-mt-d -lboost_iostreams-mt-d -lboost_locale-mt-d -lboost_log-mt-d -lboost_log_setup-mt-d -lboost_math_c99-mt-d -lboost_math_c99f-mt-d -lboost_math_c99l-mt-d -lboost_math_tr1-mt-d -lboost_math_tr1f-mt-d -lboost_math_tr1l-mt-d -lboost_prg_exec_monitor-mt-d -lboost_program_options-mt-d -lboost_python-mt-d -lboost_random-mt-d -lboost_regex-mt-d -lboost_serialization-mt-d -lboost_signals-mt-d -lboost_system-mt-d -lboost_thread-mt-d -lboost_timer-mt-d -lboost_unit_test_framework-mt-d -lboost_wave-mt-d -lboost_wserialization-mt-d 
DEPENDENCIES=-lStreamCommon -lViconCGStream -lViconCGStreamClient -lViconCGStreamClientSDK -lViconDataStreamSDK_CPP -lViconDataStreamSDKCore -lViconDataStreamSDKCoreUtils 
endif
ifeq ($(CONFIG), InternalRelease)
DEFINES=-DVICON_INTERNAL_RELEASE -DNDEBUG -DTCM_LINUX -DTCM_UNIX -D_EXPORTING -DPROJECT_SOURCE_PATH=\".\" -DRELEASE_SOFTWARE 
SYSTEMINCLUDEPATHS=-isystem../../../../thirdparty/Boost/boost-1.58.0-dynamic-linux-x64/installed/include 
INCLUDEPATHS=-I. -I../.. -I.. -I. -IInternalRelease 
LIBRARYPATHS=-L../../../../lib/InternalRelease -L../../../../thirdparty/Boost/boost-1.58.0-dynamic-linux-x64/installed/lib  -L../../../../bin/InternalRelease
LIBRARIES=-lboost_atomic-mt -lboost_chrono-mt -lboost_container-mt -lboost_context-mt -lboost_coroutine-mt -lboost_date_time-mt -lboost_filesystem-mt -lboost_graph-mt -lboost_iostreams-mt -lboost_locale-mt -lboost_log-mt -lboost_log_setup-mt -lboost_math_c99-mt -lboost_math_c99f-mt -lboost_math_c99l-mt -lboost_math_tr1-mt -lboost_math_tr1f-mt -lboost_math_tr1l-mt -lboost_prg_exec_monitor-mt -lboost_program_options-mt -lboost_python-mt -lboost_random-mt -lboost_regex-mt -lboost_serialization-mt -lboost_signals-mt -lboost_system-mt -lboost_thread-mt -lboost_timer-mt -lboost_unit_test_framework-mt -lboost_wave-mt -lboost_wserialization-mt 
DEPENDENCIES=-lStreamCommon -lViconCGStream -lViconCGStreamClient -lViconCGStreamClientSDK -lViconDataStreamSDK_CPP -lViconDataStreamSDKCore -lViconDataStreamSDKCoreUtils 
endif
ifeq ($(CONFIG), Release)
DEFINES=-DNDEBUG -DTCM_OFF_SITE -DTCM_LINUX -DTCM_UNIX -D_EXPORTING -DPROJECT_SOURCE_PATH=\".\" -DRELEASE_SOFTWARE 
SYSTEMINCLUDEPATHS=-isystem../../../../thirdparty/Boost/boost-1.58.0-dynamic-linux-x64/installed/include 
INCLUDEPATHS=-I. -I../.. -I.. -I. -IRelease 
LIBRARYPATHS=-L../../../../lib/Release -L../../../../thirdparty/Boost/boost-1.58.0-dynamic-linux-x64/installed/lib  -L../../../../bin/Release
LIBRARIES=-lboost_atomic-mt -lboost_chrono-mt -lboost_container-mt -lboost_context-mt -lboost_coroutine-mt -lboost_date_time-mt -lboost_filesystem-mt -lboost_graph-mt -lboost_iostreams-mt -lboost_locale-mt -lboost_log-mt -lboost_log_setup-mt -lboost_math_c99-mt -lboost_math_c99f-mt -lboost_math_c99l-mt -lboost_math_tr1-mt -lboost_math_tr1f-mt -lboost_math_tr1l-mt -lboost_prg_exec_monitor-mt -lboost_program_options-mt -lboost_python-mt -lboost_random-mt -lboost_regex-mt -lboost_serialization-mt -lboost_signals-mt -lboost_system-mt -lboost_thread-mt -lboost_timer-mt -lboost_unit_test_framework-mt -lboost_wave-mt -lboost_wserialization-mt 
DEPENDENCIES=-lStreamCommon -lViconCGStream -lViconCGStreamClient -lViconCGStreamClientSDK -lViconDataStreamSDK_CPP -lViconDataStreamSDKCore -lViconDataStreamSDKCoreUtils 
endif

ENV_CPU=x64
BUILDDIRECTORY=../../../../../../../../Source/Build
SOURCEDIRECTORY=../../../..
PROJECTPATH=.
BINARYDIRECTORY=../../../..
INTERMEDIATEDIRECTORY=.
LIBRARYDIRECTORY=../../../../lib
OUTPUTDIRECTORY=../../../../bin

include $(BINARYDIRECTORY)/gcc.mk

HIDE_BOOST_SCRIPT=hide_boost_version_script
ifneq ($(HIDE_BOOST),)
    HIDE_BOOST_LD_PARAM= -Wl,--version-script=$(HIDE_BOOST_SCRIPT)
    HIDE_BOOST_LD_PREREQ=$(HIDE_BOOST_SCRIPT)
endif
all: all_$(CONFIG)

all_Debug: $(OUTPUTDIRECTORY)/$(CONFIG)/libViconDataStreamSDK_C.so
all_InternalRelease: $(OUTPUTDIRECTORY)/$(CONFIG)/libViconDataStreamSDK_C.so
all_Release: $(OUTPUTDIRECTORY)/$(CONFIG)/libViconDataStreamSDK_C.so

OBJECTS=$(CONFIG)/CClient.o $(CONFIG)/CRetimingClient.o

CXXFLAGS+=$(SYSTEMINCLUDEPATHS) $(INCLUDEPATHS) $(DEFINES)
CCFLAGS+=$(SYSTEMINCLUDEPATHS) $(INCLUDEPATHS) $(DEFINES)
LDFLAGS+=$(LIBRARYPATHS)
# Android toolchain does not include librt but integrates some of its functionality into Android libc.
ifndef ANDROID_TARGET_ARCH
LDFLAGS+=-lrt
endif


$(OUTPUTDIRECTORY)/Debug/libViconDataStreamSDK_C.so: makefile $(OBJECTS) $(LIBRARYDIRECTORY)/$(CONFIG)/libStreamCommon.a $(LIBRARYDIRECTORY)/$(CONFIG)/libViconCGStream.a $(LIBRARYDIRECTORY)/$(CONFIG)/libViconCGStreamClient.a $(LIBRARYDIRECTORY)/$(CONFIG)/libViconCGStreamClientSDK.a $(OUTPUTDIRECTORY)/$(CONFIG)/libViconDataStreamSDK_CPP.so $(LIBRARYDIRECTORY)/$(CONFIG)/libViconDataStreamSDKCore.a $(LIBRARYDIRECTORY)/$(CONFIG)/libViconDataStreamSDKCoreUtils.a $(HIDE_BOOST_LD_PREREQ)
	@echo \[1\;32mLinking dll $@\[0m
	@mkdir -p $(@D)
	$(LD) -fPIC -shared -Wl,--as-needed $(LDFLAGS) -o $@ $(OBJECTS) -Wl,--start-group $(DEPENDENCIES) $(LIBRARIES) -Wl,--end-group -pthread -ldl -Wl,-rpath='$$ORIGIN':$(@D) $(HIDE_BOOST_LD_PARAM)

$(OUTPUTDIRECTORY)/InternalRelease/libViconDataStreamSDK_C.so: makefile $(OBJECTS) $(LIBRARYDIRECTORY)/$(CONFIG)/libStreamCommon.a $(LIBRARYDIRECTORY)/$(CONFIG)/libViconCGStream.a $(LIBRARYDIRECTORY)/$(CONFIG)/libViconCGStreamClient.a $(LIBRARYDIRECTORY)/$(CONFIG)/libViconCGStreamClientSDK.a $(OUTPUTDIRECTORY)/$(CONFIG)/libViconDataStreamSDK_CPP.so $(LIBRARYDIRECTORY)/$(CONFIG)/libViconDataStreamSDKCore.a $(LIBRARYDIRECTORY)/$(CONFIG)/libViconDataStreamSDKCoreUtils.a $(HIDE_BOOST_LD_PREREQ)
	@echo \[1\;32mLinking dll $@\[0m
	@mkdir -p $(@D)
	$(LD) -fPIC -shared -Wl,--as-needed $(LDFLAGS) -o $@ $(OBJECTS) -Wl,--start-group $(DEPENDENCIES) $(LIBRARIES) -Wl,--end-group -pthread -ldl -Wl,-rpath='$$ORIGIN':$(@D) $(HIDE_BOOST_LD_PARAM)

$(OUTPUTDIRECTORY)/Release/libViconDataStreamSDK_C.so: makefile $(OBJECTS) $(LIBRARYDIRECTORY)/$(CONFIG)/libStreamCommon.a $(LIBRARYDIRECTORY)/$(CONFIG)/libViconCGStream.a $(LIBRARYDIRECTORY)/$(CONFIG)/libViconCGStreamClient.a $(LIBRARYDIRECTORY)/$(CONFIG)/libViconCGStreamClientSDK.a $(OUTPUTDIRECTORY)/$(CONFIG)/libViconDataStreamSDK_CPP.so $(LIBRARYDIRECTORY)/$(CONFIG)/libViconDataStreamSDKCore.a $(LIBRARYDIRECTORY)/$(CONFIG)/libViconDataStreamSDKCoreUtils.a $(HIDE_BOOST_LD_PREREQ)
	@echo \[1\;32mLinking dll $@\[0m
	@mkdir -p $(@D)
	$(LD) -fPIC -shared -Wl,--as-needed $(LDFLAGS) -o $@ $(OBJECTS) -Wl,--start-group $(DEPENDENCIES) $(LIBRARIES) -Wl,--end-group -pthread -ldl -Wl,-rpath='$$ORIGIN':$(@D) $(HIDE_BOOST_LD_PARAM)

# Source Files
$(CONFIG)/CClient.o: makefile $(SOURCEDIRECTORY)/Vicon/CrossMarket/DataStream/ViconDataStreamSDK_C/CClient.cpp
	@echo \[1\;34mCompiling CClient.cpp\[0m
	@mkdir -p $(@D)
	find $(CONFIG) -name *.gch -exec cp '{}' . \;
	$(CXX) -fPIC -MMD -MP -I$(CONFIG)/ $(CXXFLAGS)  -o $@ -c $(SOURCEDIRECTORY)/Vicon/CrossMarket/DataStream/ViconDataStreamSDK_C/CClient.cpp

-include $(CONFIG)/CClient.d

$(CONFIG)/CRetimingClient.o: makefile $(SOURCEDIRECTORY)/Vicon/CrossMarket/DataStream/ViconDataStreamSDK_C/CRetimingClient.cpp
	@echo \[1\;34mCompiling CRetimingClient.cpp\[0m
	@mkdir -p $(@D)
	find $(CONFIG) -name *.gch -exec cp '{}' . \;
	$(CXX) -fPIC -MMD -MP -I$(CONFIG)/ $(CXXFLAGS)  -o $@ -c $(SOURCEDIRECTORY)/Vicon/CrossMarket/DataStream/ViconDataStreamSDK_C/CRetimingClient.cpp

-include $(CONFIG)/CRetimingClient.d

# Header Files
# Resource Files
# Other Files

clean:
	@echo \[1\;31mCleaning $(CONFIG) build\[0m
	find . -path '*/$(CONFIG)/*' \( -name '*.[od]' -o -name '*.gch' \) -exec rm -f {} ';' 
	rm -f moc_*.cxx

$(HIDE_BOOST_SCRIPT): makefile
	echo -n >$@
	echo "{" >>$@
	echo "  local: *N5boost*; *NK5boost*;" >>$@
	echo "};" >>$@
