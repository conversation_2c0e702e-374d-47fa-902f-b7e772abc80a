#pragma once

#define VICONDATASTREAMSDKCOREUTILS_VERSION_REVISION_CONTROL_SYSTEM "unknown"
#define VICONDATASTREAMSDKCOREUTILS_VERSION_REVISION 0
#define VICONDATASTREAMSD<PERSON>COREUTILS_VERSION_MONOTONIC_REVISION 0
#define VICONDATASTREAMSDKCOREUTILS_VERSION_PUBLIC_REVISION_STRING "0"
#define VICONDATASTREAMSDKCOREUTILS_VERSION_BRANCH "unknown"
#define VICONDATASTREAMSDKCOREUTILS_VERSION_REPOSITORY "unknown"
#define VICONDATASTREAMSDKCOREUTILS_VERSION_CHANGESET "0"
#define VICONDATASTREAMSDKCOREUTILS_VERSION_LOCAL_CHANGESET "0"
#define VICONDATASTREAMSDKCOREUTILS_VERSION_LOCAL_BRANCH "unknown"

#define VICONDATASTREAMSDKCOREUTILS_VERSION_MAJOR 1
#define VICONDATASTREAMSDKCOREUTILS_VERSION_MINOR 11
#define VICONDATASTREAMSDKCOREUTILS_VERSION_POINT 0
#define VICONDATASTREAMSDKCOREUTILS_VERSION_RELEASE ""
#define VICONDATASTREAMSDKCOREUTILS_VERSION_SUPPLEMENTAL ""

#define VICONDATASTREAMSDKCOREUTILS_FULL_VERSION_STRING "1.11.0.0"
#define VICONDATASTREAMSDKCOREUTILS_VERSION_STRING "1.11"
#define VICONDATASTREAMSDKCOREUTILS_COMPANY "Vicon Motion Systems Ltd"
#define VICONDATASTREAMSDKCOREUTILS_COPYRIGHT "Copyright \x00A9 2020 Vicon Motion Systems Ltd. All rights reserved."
#define VICONDATASTREAMSDKCOREUTILS_TRADEMARK "Vicon\x00AE is a registered trademark of OMG Plc."
#define VICONDATASTREAMSDKCOREUTILS_PROJECT_NAME "ViconDataStreamSDKCoreUtils"

#define VICONDATASTREAMSDKCOREUTILS_FULL_VERSION_STRING_L L"1.11.0.0"
#define VICONDATASTREAMSDKCOREUTILS_VERSION_STRING_L L"1.11"
#define VICONDATASTREAMSDKCOREUTILS_COMPANY_L L"Vicon Motion Systems Ltd"
#define VICONDATASTREAMSDKCOREUTILS_COPYRIGHT_L L"Copyright \x00A9 2020 Vicon Motion Systems Ltd. All rights reserved."
#define VICONDATASTREAMSDKCOREUTILS_TRADEMARK_L L"Vicon\x00AE is a registered trademark of OMG Plc."
#define VICONDATASTREAMSDKCOREUTILS_PROJECT_NAME_L L"ViconDataStreamSDKCoreUtils"
