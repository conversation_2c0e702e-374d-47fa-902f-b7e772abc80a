cmake_minimum_required(VERSION 3.0.2)
project(robot_ctrl)

find_package(catkin REQUIRED COMPONENTS
  roscpp
  std_msgs
  geometry_msgs
  sensor_msgs
  message_generation
  board_com
)

include_directories(
  include
  ${catkin_INCLUDE_DIRS}
)

generate_messages(
  DEPENDENCIES
  std_msgs
)

catkin_package(
  CATKIN_DEPENDS message_runtime board_com
)
add_executable(robot_ctrl_demo1 
  src/robot_ctrl_demo1.cc
)
target_link_libraries(robot_ctrl_demo1
  ${catkin_LIBRARIES}
)
