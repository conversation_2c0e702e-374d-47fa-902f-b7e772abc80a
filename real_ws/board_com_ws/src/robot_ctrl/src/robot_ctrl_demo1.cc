#include <iostream>
#include "robot_ctrl.h"

/**
 * @brief main
 * 
 */
int main(int argc, char *argv[])
{
    ros::init(argc,argv,"ctrldemo");
    std::cout << argv[2]<<std::endl; 
    auto node = std::make_shared<RobotCtrl>();
    ros::Rate loop_rate(1000);
    while(ros::ok())
    {
        node->MoveTask();
        ros::spinOnce();
        loop_rate.sleep();
    }
    return 0;
}
/**
 * @brief Construct a new Robot Ctrl:: Robot Ctrl object
 * 
 */
RobotCtrl::RobotCtrl()
{
    /* 参数服务器 */
    ros::param::param<int>("~mode_type", this->mode_type_, 0);
    this->pub_velcmd_ = this->advertise<geometry_msgs::Twist>("robot/velcmd",10);
    this->pub_ledupcmd_ = this->advertise<std_msgs::UInt32MultiArray>("robot/ledup",10);
    this->pub_leddowncmd_ = this->advertise<std_msgs::UInt32MultiArray>("robot/leddown",10);
    this->client_sscmd_ = this->serviceClient<board_com::sscmd>("sscmd");
    this->RobotStart(1);
}
/**
 * @brief Destroy the Robot Ctrl:: Robot Ctrl object
 * 
 */
RobotCtrl::~RobotCtrl() 
{
   
}

void RobotCtrl::RobotStart(int flag)
{
    board_com::sscmd cmd;
    cmd.request.cmd = 1;
    bool sflag = this->client_sscmd_.call(cmd);
    if (sflag){

    }
    else{

    }
}
void RobotCtrl::MoveTask(void)
{
    static uint32_t tick = 0;
    tick ++;
    
    if(tick % 10 ==0){
        if(this->mode_type_ == 0){
            this->MoveStraightLoop();
        }else if(this->mode_type_ == 1){
             this->MoveCircle();
        }  
      
    }
    if(tick % 100 ==0){
        if(this->mode_type_ == 2){
            this->LedUpCtrl();
            this->LedDownCtrl();
        }  
    }
    
}
uint32_t GetRGB32Data(uint8_t r, uint8_t g, uint8_t b, uint8_t num)
{
    return uint32_t((uint32_t)num<<24|(uint32_t)g<<16|(uint32_t)r<<8|b);
}

void RobotCtrl::LedUpCtrl(void)
{
    uint32_t data;
    uint8_t all_num = 0;
    /*设定了三种颜色 */
    this->ledupmsg_.data.clear();
    data = GetRGB32Data(0xff,0x00,0x00,14);
    this->ledupmsg_.data.push_back(data);
    data = GetRGB32Data(0x00,0xff,0x00,14);
    this->ledupmsg_.data.push_back(data);
    data = GetRGB32Data(0x00,0x00,0xff,14);
    this->ledupmsg_.data.push_back(data);
    for(uint8_t i = 0; i < this->ledupmsg_.data.size(); i++){
        all_num += this->ledupmsg_.data[i]>>24;
        if(all_num > 42){
            ROS_ERROR(" max led1 %d", all_num);
            return;
        }
    }
    this->pub_ledupcmd_.publish(this->ledupmsg_);
}

void RobotCtrl::LedDownCtrl(void)
{
    uint32_t data;
    uint8_t all_num = 0;
    /*设定了三种颜色 */
    this->leddownmsg_.data.clear();
    data = GetRGB32Data(0xff,0xff,0x00,30);
    this->leddownmsg_.data.push_back(data);
    data = GetRGB32Data(0x00,0xff,0x00,30);
    this->leddownmsg_.data.push_back(data);
    data = GetRGB32Data(0x00,0x00,0xff,29);
    this->leddownmsg_.data.push_back(data);
    for(uint8_t i = 0; i < this->leddownmsg_.data.size(); i++){
        all_num += this->leddownmsg_.data[i]>>24;
        if(all_num > 89){
            ROS_ERROR(" max led2");
            return;
        }
    }
    this->pub_leddowncmd_.publish(this->leddownmsg_);
}

void RobotCtrl::MoveCircle(void)
{
    uint8_t time_dev = 4;
    static uint32_t tick = 0;
    static float time = 0;
    tick++;
    time = tick / 100 / time_dev;
    this->velcmd_.linear.x = 0.25f * sin(tick * 2 * 3.14f);
    this->velcmd_.linear.y = 0.25f * cos(tick * 2 * 3.14f);
    this->velcmd_.angular.z = 0.0f;
    this->pub_velcmd_.publish(this->velcmd_);
}

void RobotCtrl::MoveStraightLoop(void)
{
    static uint32_t tick = 0;
    tick++;
    if(tick<400){
        this->velcmd_.linear.x = 0.5f;
        this->velcmd_.linear.y = 0.0f;
        this->velcmd_.angular.z = 0.0f;
    }else if(tick>=400 && tick < 800){
        this->velcmd_.linear.x = -0.5f;
        this->velcmd_.linear.y = 0.0f;
        this->velcmd_.angular.z = 0.0f;
    }else{
        tick = 0;
    }
    this->pub_velcmd_.publish(this->velcmd_);
}