#ifndef ROBOT_CTRL_H
#define ROBOT_CTRL_H

#include "ros/ros.h"
#include "std_msgs/String.h"
#include "geometry_msgs/Twist.h"
#include "sensor_msgs/Imu.h"
#include "board_com/sscmd.h"
#include <ros/service_server.h> 
#include "std_msgs/UInt32MultiArray.h"

#include "stdint.h"
#include <string.h>
#include "stdbool.h"
#include <stdio.h>
#include <stdarg.h> 

class RobotCtrl : public ros::NodeHandle
{
public:
    RobotCtrl();
    ~RobotCtrl();    
    void MoveTask(void);
    void RobotStart(int flag);
private:
    int mode_type_;
    void MoveStraightLoop(void);
    void MoveCircle(void);
    void LedUpCtrl(void);
    void LedDownCtrl(void);
    geometry_msgs::Twist velcmd_;
    std_msgs::UInt32MultiArray ledupmsg_;
    std_msgs::UInt32MultiArray leddownmsg_;
    ros::Publisher pub_velcmd_;
    ros::Publisher pub_ledupcmd_;
    ros::Publisher pub_leddowncmd_;
    ros::ServiceClient client_sscmd_;
};

#endif