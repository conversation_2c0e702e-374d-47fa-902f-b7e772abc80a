#ifndef BOARD_COM_H
#define BOARD_COM_H

#include "ros/ros.h"
#include <ros/service_server.h> 
#include <serial/serial.h>
#include "std_msgs/UInt32MultiArray.h"
#include "std_msgs/Int8.h"
#include "geometry_msgs/Twist.h"
#include "geometry_msgs/TransformStamped.h"
#include "sensor_msgs/Imu.h"
#include "sensor_msgs/NavSatFix.h"
#include "nav_msgs/Odometry.h"
#include "board_com/sscmd.h"

#include "stdint.h"
#include <string.h>
#include "stdbool.h"
#include <stdio.h>
#include <stdarg.h> 
#include <Eigen/Dense>

/* 基本解析参数 */
typedef struct
{
    uint8_t headLen;
    uint8_t addrLen;

    uint8_t head;
    uint8_t addr;
    uint8_t cmd;
    uint8_t dataLen;
    uint8_t dataBuf[0xFF];
    uint8_t sumCheck;
    uint8_t addCheck;
	/* 总字节数 */
	uint64_t length;
	/* 校验成功与失败次数 */
	uint64_t checkNum[2];
	float errorRate;	
	bool linkState;
	
}link_t;

class LinkComm : public ros::NodeHandle
{
public:
    LinkComm();
    ~LinkComm();    
    void Serial_GetData(void);
    void Link_SendLoop(void);
    bool StartStopCmdSrvCallBack(board_com::sscmd::Request& req,board_com::sscmd::Response& resp);
    void VelCmdMsgCallBack(const geometry_msgs::Twist::ConstPtr& msg_p);
    void ViconMsgCallBack(const geometry_msgs::TransformStamped::ConstPtr& msg_p);
    void LedUpMsgCallBack(const std_msgs::UInt32MultiArray::ConstPtr& msg_p);
    void LedDownMsgCallBack(const std_msgs::UInt32MultiArray::ConstPtr& msg_p);
private:
    link_t  receive_;
    link_t  send_;
    serial::Serial serial_;
    std::string portname_;
    std::string hostname_;
    std::stringstream vicon_topic_name_;
    uint8_t vswarm_id_;
    /* 摄像头异常指示 */
    uint8_t camera_useful_;
    /* 电池电量 */
    uint8_t battery_;
    /* imu状态 */
    uint8_t imu_state_;
    /* gps状态 */
    uint8_t gps_state_; 
    /* vicon数据 */
    Eigen::Vector3d vicon_angle_;
    Eigen::Vector3d vicon_pos_;
    /* 里程计数据 */
    Eigen::Vector3f odom_pos_;
    Eigen::Vector3f odom_vel_;
    /* GPS数据 */
    Eigen::Vector3d gps_data_;
    Eigen::Vector3f mag_;
    /* 灯珠颜色状态数组 */
    uint32_t ledupArray_[43];
    uint8_t ledup_size_;
    uint32_t leddownArray_[89];
    uint8_t leddown_size_;
    void Link_Receive(uint8_t data);
    void Link_Decode(void);
    void Link_Check(link_t *link);
    void Link_Init(void);
    void Link_AddSend(link_t *link);
    void PrintHexData(const std::string &data); 

    void Link_Heartbeat(void);
    void Link_SendControlCmd(bool flag);
    void Link_SendVelCmd(void);
    void Link_SendVicon(void);
    void Link_LedUP(void);
    void Link_LedDown(void);

    void Link_HeartbeatDecode(void);
    void Link_ImuDecode(void);
    void Link_GPSDecode(void);
    void Link_OdometerDecode(void);
    
    void ImuMsgPublish(void);
    void BattaryMsgPublish(void);
    void GPSMsgPublish(void);
    void OdometryMsgPublish(void);
    ros::ServiceServer ser_sscmd_;
    geometry_msgs::Twist velcmd_msg_;

    sensor_msgs::Imu imu_data_msg_;
    std_msgs::Int8 battary_msg_;
    sensor_msgs::NavSatFix gps_msg_;
    nav_msgs::Odometry odometry_msg_;

    ros::Publisher pub_imu_;
    ros::Publisher pub_battery_;
    ros::Publisher pub_gps_;
    ros::Publisher pub_odometry_;
    ros::Subscriber sub_velcmd_;
    ros::Subscriber sub_vicon_;
    ros::Subscriber sub_ledup_;
    ros::Subscriber sub_leddown_;
};


#endif