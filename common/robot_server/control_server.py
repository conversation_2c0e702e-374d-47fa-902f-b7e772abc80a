import socket
import threading
import time
import rospy
from std_msgs.msg import UInt32MultiArray
from geometry_msgs.msg import Twist, TransformStamped, PoseStamped
from sensor_msgs.msg import CompressedImage
import os
from paho.mqtt import client as mqtt_client
import json
import cmd

broker = 0
port = 1883
keepalive = 60     # 与代理通信之间允许的最长时间段（以秒为单位）              

client_id = f'{socket.gethostname()}_robot'  # 客户端id不能重复

broker_ip = "*********"


hostname = f"/{client_id}" 

class mqtt_client_thread():
    def __init__(self, broker, port, keepalive, client_id):
        super(mqtt_client_thread, self).__init__()
        self.broker = broker  # mqtt代理服务器地址
        self.port = port
        self.keepalive = keepalive 
        self.client_id = client_id
        self.cam0_enable = 0
        self.cam1_enable = 0
        self.cam2_enable = 0 
        self.cam3_enable = 0 
        self.position_enable = 0 
        self.led_up = []
        self.led_down = []       
        self.client = self.connect_mqtt()
        self.client.on_message = self.mqtt_callback
        self.client.subscribe(hostname + '/cmd')
        self.client.subscribe(hostname + '/motion')
        self.client.subscribe(hostname + '/com')
        self.client.subscribe('/Broadcast/cmd')
        self.pos_update_interval = 5
        self.pos_update_cnt = 0
        rospy.init_node("robotCtrl")
        self.vel_pub = rospy.Publisher('robot/velcmd', Twist, queue_size=10)
        self.ledup_pub = rospy.Publisher('robot/ledup', UInt32MultiArray, queue_size=10)
        self.leddown_pub = rospy.Publisher('robot/leddown', UInt32MultiArray, queue_size=10)
        self.positon_goal = rospy.Publisher('/point_control/goal', PoseStamped, queue_size=10)
        
        rospy.Subscriber('/cam0/image_raw/compressed',CompressedImage, self.photograph_callback0)
        rospy.Subscriber('/cam1/image_raw/compressed',CompressedImage, self.photograph_callback1)
        rospy.Subscriber('/cam2/image_raw/compressed',CompressedImage, self.photograph_callback2)
        rospy.Subscriber('/cam3/image_raw/compressed',CompressedImage, self.photograph_callback3)
        rospy.Subscriber(f'/vicon/{socket.gethostname()}/{socket.gethostname()}',TransformStamped, self.position_callback)
        
    def connect_mqtt(self):
        '''连接mqtt代理服务器'''
        def on_connect(client, userdata, flags, rc):
            '''连接回调函数'''
            # 响应状态码为0表示连接成功
            if rc == 0:
                print("Connected to MQTT OK!")
            else:
                print("Failed to connect, return code %d", rc)
        # 连接mqtt代理服务器，并获取连接引用
        client = mqtt_client.Client(self.client_id)
        client.on_connect = on_connect
        client.connect(self.broker, self.port, self.keepalive)
        return client       
    def mqtt_callback(self, client, userdata, msg):
        '''订阅消息回调函数'''
        if msg.topic == ('/' +self.client_id + '/motion'):
            
            motion_msg = json.loads(msg.payload.decode())
            #print(cmd_msg)
            
            vel3 = Twist()
            vel3.linear.x = float(motion_msg['x'])
            vel3.linear.y = float(motion_msg['y'])
            vel3.angular.z = float(motion_msg['theta'])
            self.vel_pub.publish(vel3)
        if msg.topic == ('/' +self.client_id + '/cmd') or msg.topic == '/Broadcast/cmd':
            cmd_msg = json.loads(msg.payload.decode())
            
            if cmd_msg['cmd_type'] == 'cam':
                cam_id = cmd_msg['args']['0']
                if cam_id == '0':
                    self.cam0_enable = cmd_msg['args']['1']
                elif cam_id == '1':
                    self.cam1_enable = cmd_msg['args']['1']
                elif cam_id == '2':
                    self.cam2_enable = cmd_msg['args']['1']
                else:
                    self.cam3_enable = cmd_msg['args']['1']
            elif cmd_msg['cmd_type'] == 'motion':
                vel3 = Twist()
                vel3.linear.x = float(cmd_msg['args']['0'])
                vel3.linear.y = float(cmd_msg['args']['1'])
                vel3.angular.z = float(cmd_msg['args']['2'])
                self.vel_pub.publish(vel3)
            elif cmd_msg['cmd_type'] == 'pos':
                self.position_enable = cmd_msg['args']['0']
            elif cmd_msg['cmd_type'] == 'ledup':
                args_length = int(cmd_msg['args_length'] / 2)
                self.led_up.clear()
                for i in range(args_length):
                    color = cmd_msg['args'][str(i * 2)]
                    num = cmd_msg['args'][str(i * 2 + 1)]        
                    self.led_up.append(GetRGB32Data((color >> 16 & 0xff),(color >>8 & 0xff),color & 0xff, num))
            elif cmd_msg['cmd_type'] == 'leddown':

                args_length = int(cmd_msg['args_length'] / 2)
                self.led_down.clear()
                for i in range(args_length):
                    color = cmd_msg['args'][str(i * 2)]
                    num = cmd_msg['args'][str(i * 2 + 1)]        
                    self.led_down.append(GetRGB32Data((color >> 16 & 0xff),(color >>8 & 0xff),color & 0xff, num))
                    
            elif cmd_msg['cmd_type'] == 'pos_ctrl':
                x = cmd_msg['args']['0']
                y = cmd_msg['args']['1']
                goal = PoseStamped()
                goal.pose.position.x = float(x)
                goal.pose.position.y = float(y)
                self.positon_goal.publish(goal)

    def publish(self, client, topic, msg):
        result = client.publish(topic, msg)
        status = result[0]
        if status == 0:
            #print(f"Send `{len(msg)}` to topic `{topic}`")
            pass
        else:
            print(f"Failed to send message to topic {topic}")
    def photograph_callback0(self, cam_msg):
        if self.cam0_enable:
            self.publish(self.client, hostname + '/cam/0', bytearray(cam_msg.data))
        
    def photograph_callback1(self, cam_msg):
        if self.cam1_enable:
            self.publish(self.client, hostname + '/cam/1', bytearray(cam_msg.data))

    def photograph_callback2(self, cam_msg):
        if self.cam2_enable:
            self.publish(self.client, hostname + '/cam/2', bytearray(cam_msg.data))

    def photograph_callback3(self, cam_msg):
        if self.cam3_enable:
            self.publish(self.client, hostname + '/cam/3', bytearray(cam_msg.data))
        
    def position_callback(self, transform_msg):
        if self.position_enable:
            if self.pos_update_cnt == self.pos_update_interval:
                pos_msg = {
                    'x':transform_msg.transform.translation.x,
                    'y':transform_msg.transform.translation.y,
                    'z':transform_msg.transform.translation.z
                }    
                self.publish(self.client, hostname + '/pos', json.dumps(pos_msg))
                self.pos_update_cnt = 0
            self.pos_update_cnt += 1
            #print("success")        
    def run(self):
        self.client.loop_forever()

class user_cmd_input(cmd.Cmd):
    intro = ">>Welcome to the CLI. Type 'help' to list commands."
    prompt = ">>"
    def __init__(self):
        super(user_cmd_input, self).__init__()
    def do_quit(self, arg):
        """Exit the CLI."""
        print("Exiting...")
        os._exit(0)
    #空发送时执行命令
    def emptyline(self):
        return
     #找不到指令
    def default(self, line):
        self.stdout.write('%s:not found\n'%line)
def GetRGB32Data(r, g,  b, num):
    return (num<<24) +(g << 16) + (r << 8) + b 
       
if __name__ == "__main__":
    
    try:
        broker = os.environ['REMOTE_SERVER']
    except:
        broker = broker_ip
    net_status = -1
    while net_status != 0:
        net_status = os.system(f"ping -c 4 {broker}")
        time.sleep(2)
     
    # 启动MQTT客户端的线程
    mqtt_client_instance = mqtt_client_thread(broker=broker, 
                                           port=port, 
                                           keepalive=keepalive, 
                                           client_id=client_id
                                           )
    mqtt_thread = threading.Thread(target=mqtt_client_instance.run)
    mqtt_thread.start()
    client = mqtt_client_instance
    # 启动Cli线程 
    time.sleep(1)

    user_cmd_instance = user_cmd_input()
    cli_thread = threading.Thread(target=user_cmd_instance.cmdloop)
    cli_thread.start()
    time.sleep(1)
    
    ros_hz = 100
    rate = rospy.Rate(ros_hz)
    while not rospy.is_shutdown():
        try:
            ledup_msg = UInt32MultiArray(None,client.led_up)
            client.ledup_pub.publish(ledup_msg)
            leddown_msg = UInt32MultiArray(None,client.led_down)
            client.leddown_pub.publish(leddown_msg)
            rate.sleep()
        except rospy.ROSInterruptException:
            rospy.logerr("ROS Interrupt Exception! Just ignore the exception!")
        except rospy.ROSTimeMovedBackwardsException:
            rospy.logerr("ROS Time Backwards! Just ignore the exception!")
