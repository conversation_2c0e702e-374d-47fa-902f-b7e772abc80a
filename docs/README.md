# 如何启动

## 1.打开小车通信server
首先需要打开小车端的server，使其可以接收指令和上传自身相关信息。首先打开一个终端，输入以下命令：
```
$ roscore
$ python3 common/robot_server/control_server.py
```
control_server会先ping MQTT的broker四次，成功连接broker及ROS后会进入一个cmd shell等待输入的状态
```
PING 10.0.2.66 (10.0.2.66) 56(84) bytes of data.
64 字节，来自 10.0.2.66: icmp_seq=1 ttl=60 时间=2.76 毫秒
64 字节，来自 10.0.2.66: icmp_seq=2 ttl=60 时间=2.54 毫秒
64 字节，来自 10.0.2.66: icmp_seq=3 ttl=60 时间=3.13 毫秒
64 字节，来自 10.0.2.66: icmp_seq=4 ttl=60 时间=4.89 毫秒

--- 10.0.2.66 ping 统计 ---
已发送 4 个包， 已接收 4 个包, 0% 包丢失, 耗时 3005 毫秒
rtt min/avg/max/mdev = 2.539/3.331/4.892/0.926 ms
Connected to MQTT OK!
>>Welcome to the CLI. Type 'help' to list commands.
>>
```
以上状态即代表control_server启动成功

## 2.小车功能使能
小车支持接入仿真环境和真实环境。在不同环境下对下层的接口不同，因此有两种启动流程，通过使能层可以实现底层功能的抽象，使得功能算法部分可以使用相同的接口对真实/仿真环境中的小车实现相同的控制。

### 2.1 仿真环境
仿真环境的启动由两步构成
#### 2.1.1 开启gazebo环境
再开启一个终端，输入以下命令：
```
$ cd sim_ws/vswarm_sim_ws/
$ source source devel/setup.bash
$ roslaunch vswarm_sim gazebo.launch
```
使用以上命令可以成功开启gazebo的仿真环境，默认场景是IUSL106的实验室

#### 2.1.2 加载小车
gazebo的仿真环境成功开启后，需要在gazebo中加载生成小车，再开启一个终端，输入以下命令：
```
$ cd sim_ws/vswarm_sim_ws/
$ python3 src/vswarm_sim/scripts/swarmsim.py
```
执行以上命令后，该脚本会向仿真服务器注册该小车的信息，并开始同步所有小车的信息，使得可以在仿真环境中显示所有参与仿真的小车。

### 2.2 真实环境

To be added


完成以上步骤后，小车即可以在真实/仿真环境中正常进行使用，此时可以在小车端或管理端分别使用速度信息进行控制，具体控制方法可以参考xxx
## 3.位置控制功能开启(Optinal)
如需使用位置控制功能，需开启新的终端，使用以下命令：
```
$ python3 common/point_control/pointControl.py
```
在小车端通过向/point_control/goal话题中发布 geometry_msgs/PoseStamped类型的数据；在管理终端可以通过 ```pcontrol -a agent_id x_site y_site```实现位置控制功能。