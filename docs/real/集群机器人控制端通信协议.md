# 集群机器人交互

## 板间通信

### 协议格式

**基本格式**

​	基本格式如下，第二位地址位中`AA`为主机，`55`为从机。

| 帧头     | 地址        | 功能码    | 长度 | 数据 | 校验 |
| -------- | ----------- | --------- | ---- | ---- | ---- |
| FE(1bit) | AA/55(1bit) | cmd(1bit) | 1bit | nbit | 2bit |

校验方法

- **sum1**：从帧头计算，计算至数据结束，求和位数为1+1+1+1+n，取低八位。
- **sum2**：进行每一字节的加法运算，同时进行一次sum1的累加操作，求和位数为1+1+1+1+n+1（sum1），取低八位。

### 协议内容

​	以下速度单位m/s，角速度单位deg/s，角度单位为度。

**主从内容**

​	计算板（0xAA）——控制板（0x55）

- **0x01**：心跳包（1hz）

  | 数据类型 | uint8                | uint8      |
  | -------- | -------------------- | ---------- |
  | 数据内容 | ID（由Hostname计算） | 摄像头状态 |

- **0x18**：vicon数据（100hz）

  | 数据类型 | float   | float   | float   | float      | float      | float      |
  | -------- | ------- | ------- | ------- | ---------- | ---------- | ---------- |
  | 数据内容 | angle_x | angle_y | angle_z | position_x | position_y | position_z |
  
- **0x50**：启动停止

  | 数据类型 | uint8          |
  | -------- | -------------- |
  | 数据内容 | 0/1——启动/停止 |

- **0x60**：速度指令（100hz）

  | 数据类型 | float | float | float  |
  | -------- | ----- | ----- | ------ |
  | 数据内容 | vel_x | vel_x | rate_z |

  xy为线速度单位m/s，z为角速度单位deg/s。

- **0x70**：上灯带

  | 数据类型 | unit32     | ...  |
  | -------- | ---------- | ---- |
  | 数据内容 | 24bit+8bit | ...  |

  灯带格式为24位BRG（颜色）+8位（数量）的格式，最大数量为42。

- **0x71**：下灯带

  | 数据类型 | unit32     | ...  |
  | -------- | ---------- | ---- |
  | 数据内容 | 24bit+8bit | ...  |

  灯带格式为24位BRG（颜色）+8位（数量）的格式，最大数量为89。

**从主内容**

​	控制板（0x55）—— 计算板（0xAA）

- **0x01**：心跳包（1hz）

  | 数据类型 | uint8       | uint8   | uint8   |
  | -------- | ----------- | ------- | ------- |
  | 数据内容 | 电量值0-100 | IMU状态 | GPS状态 |

- **0x10**：IMU数据（100hz）

    | 数据类型 | float | float | float | float  | float  | float  | float | float | float | float |
    | -------- | ----- | ----- | ----- | ------ | ------ | ------ | ----- | ----- | ----- | ----- |
    | 数据内容 | acc_x | acc_y | acc_z | gyro_x | gyro_y | gyro_z | q0    | q1    | q2    | q3    |

- **0x11**：GPS数据（10hz）

    | 数据类型 | float | float | float | float | double | double |
    | -------- | ----- | ----- | ----- | ----- | ------ | ------ |
    | 数据内容 | mag_x | mag_y | mag_z | 高度  | 经度   | 纬度   |


- **0x12**：里程计（10hz）

  | 数据类型 | float | float | float  | float | float |
  | -------- | ----- | ----- | ------ | ----- | ----- |
  | 数据内容 | vel_x | vel_y | rate_z | pos_x | pos_y |

  以上所有参数均在世界系下，自启动以来。

### 灯光交互

**常规交互**

- 控制板上电过程中，下灯带灯光逐渐由暗到亮，1.5s后开机并发出两声滴滴声，此时机器人显示为电量信息（绿色灯珠数代表电量多少，亏电全红）。
- 完成上电后，控制板开启板载计算机，上灯带为全绿呼吸模式，等待与板载计算机间的通信。
- 通信成功后，灯光由板载计算机控制，并发出一声长滴声，若无灯光控制则关闭上灯光，下灯光继续显示电量。

**异常交互**

​	异常后，控制板接管灯光控制。

​	**亏电异常**：下灯带全红。

​	**摄像头异常**：上灯黄色呼吸。

​	**传感器异常**：上灯蓝色呼吸。

### 屏幕信息

​	屏幕显示为三个界面，为每10s自动切换。

- tile1：显示设备id。
- tile2：显示基本信息与状态。
- tile3：显示vicon或者slam环境下的位置坐标。
