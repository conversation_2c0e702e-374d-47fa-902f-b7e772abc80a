<launch>
  <param name="robot_description" command="$(find xacro)/xacro $(find vswarm_sim)/urdf/car.urdf.xacro" />
  <param name="robot_nocam_description" command="$(find xacro)/xacro $(find vswarm_sim)/urdf/car_nocam.urdf.xacro" />
  <include file="$(find gazebo_ros)/launch/empty_world.launch">
    <arg name="world_name" value="$(find vswarm_sim)/world/lab_106_noob.world" />
    <arg name="use_sim_time" value="true"/>
    <arg name="gui" value="true"/>
  </include>


  <node
    name="tf_footprint_base"
    pkg="tf2_ros"
    type="static_transform_publisher"
    args="0 0 0 0 0 0 1 base_footprint world" />
  <node
    name="tf_foottoworld"
    pkg="tf2_ros"
    type="static_transform_publisher"
    args="0 0 0 0 0 0 1 base_link base_footprint" />

  <node
    name="tf_odom"
    pkg="tf2_ros"
    type="static_transform_publisher"
    args="0 0 0.05 0 0 0 1 world odom_frame" />

  <node
    name="fake_joint_calibration"
    pkg="rostopic"
    type="rostopic"
    args="pub /calibrated std_msgs/Bool true" />
  <!--  
  <node pkg="vswarm_sim" type="swarmsim.py" name="swarmsim" />
  -->

</launch>
