<robot name="imu" xmlns:xacro="http://wiki.ros.org/xacro">  
    <xacro:property name="imu_offset_x" value="0" />
    <xacro:property name="imu_offset_y" value="0" />
    <xacro:property name="imu_offset_z" value="0.02" />
    <xacro:property name="imu_size"     value="0.01" />
    <xacro:property name="imu_m" value="0.01" /> <!-- imu质量 -->
    <xacro:macro name="Box_inertial_matrix" params="m l w h">
       <inertial>
               <mass value="${m}" />
               <inertia ixx="${m*(h*h + l*l)/12}" ixy = "0" ixz = "0"
                   iyy="${m*(w*w + l*l)/12}" iyz= "0"
                   izz="${m*(w*w + h*h)/12}" />
       </inertial>
   </xacro:macro>
    <!-- imu -->
    <joint name="imutoCar" type="fixed">
        <origin xyz="${imu_offset_x} ${imu_offset_y} ${imu_offset_z}" rpy="0 0 0" />
        <parent link="base_link"/>
        <child link="imu"/>
    </joint>
        
    <link name="imu">
        <visual>
            <origin rpy="0 0 0" xyz="0 0 0" />
            <geometry>
                    <box size="${imu_size} ${imu_size} ${imu_size}"/>
            </geometry>                
            <material name= "red" >
              <color rgba="1.0 0.0 0.0 1.0" />
            </material>
        </visual>
        <collision>
            <geometry>
                <box size="${imu_size} ${imu_size} ${imu_size}" />
            </geometry>
            <origin xyz="0.0 0.0 0" rpy="0.0 0.0 0.0" />
        </collision>
        <xacro:Box_inertial_matrix m = "${imu_m}" l = "${imu_size}" w = "${imu_size}" h = "${imu_size}"/>
    </link>

    <gazebo reference="imu">
        <material>Gazebo/Red</material>
    </gazebo>
</robot>