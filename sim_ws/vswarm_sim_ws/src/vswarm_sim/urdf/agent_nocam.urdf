<?xml version="1.0" encoding="utf-8"?>
<!-- This URDF was automatically created by SolidWorks to URDF Exporter! Originally created by <PERSON> (<EMAIL>) 
     Commit Version: 1.6.0-4-g7f85cfe  Build Version: 1.6.7995.38578
     For more information, please see http://wiki.ros.org/sw_urdf_exporter -->
<robot
  name="vswarm_sim">
  <link
    name="base_link">
    <inertial>
      <origin
        xyz="-7.189E-06 1.4634E-12 0.024266"
        rpy="0 0 0" />
      <mass
        value="1.8" />
      <inertia
        ixx="0.1"
        ixy="-2.7028E-13"
        ixz="2.3291E-06"
        iyy="0.1"
        iyz="8.9803E-13"
        izz="0.0070082" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 3.14" />
      <geometry>
        <mesh
          filename="package://vswarm_sim/meshes/base_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.79216 0.81961 0.93333 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 3.14" />
      <geometry>
        <mesh
          filename="package://vswarm_sim/meshes/base_link.STL" />
      </geometry>
    </collision>
  </link>
  <link
    name="lower_link">
    <inertial>
      <origin
        xyz="0.0015929 -2.4762E-11 0.039254"
        rpy="0 0 0" />
      <mass
        value="0.3" />
      <inertia
        ixx="0.0014457"
        ixy="-2.1532E-10"
        ixz="1.6549E-05"
        iyy="0.0015666"
        iyz="-1.4832E-11"
        izz="0.0027415" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 3.14" />
      <geometry>
        <mesh
          filename="package://vswarm_sim/meshes/lower_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.79216 0.81961 0.93333 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 3.14" />
      <geometry>
        <mesh
          filename="package://vswarm_sim/meshes/lower_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="low_link_joint"
    type="fixed">
    <origin
      xyz="-7.2222E-05 0 0.066"
      rpy="0 0 0" />
    <parent
      link="base_link" />
    <child
      link="lower_link" />
    <axis
      xyz="0 0 0" />
    <limit
      lower="-0.01"
      upper="0.01"
      effort="0"
      velocity="0" />
  </joint>
  <link
    name="upper_link">
    <inertial>
      <origin
        xyz="0.0039727 -4.3487E-09 0.024539"
        rpy="0 0 0" />
      <mass
        value="0.095" />
      <inertia
        ixx="3.5034E-05"
        ixy="-6.1451E-13"
        ixz="2.9085E-13"
        iyy="3.5034E-05"
        iyz="4.7026E-13"
        izz="6.2981E-05" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 3.14" />
      <geometry>
        <mesh
          filename="package://vswarm_sim/meshes/upper_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.79216 0.81961 0.93333 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 3.14" />
      <geometry>
        <mesh
          filename="package://vswarm_sim/meshes/upper_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="upper_link_joint"
    type="fixed">
    <origin
      xyz="-0.0039727 0 0.070397"
      rpy="0 0 0" />
    <parent
      link="lower_link" />
    <child
      link="upper_link" />
    <axis
      xyz="0 0 0" />
    <limit
      lower="-0.01"
      upper="0.01"
      effort="0"
      velocity="0" />
  </joint>
  <link
    name="light_link">
    <inertial>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <mass
        value="0.05" />
      <inertia
        ixx="0"
        ixy="0"
        ixz="0"
        iyy="0"
        iyz="0"
        izz="0" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://vswarm_sim/meshes/light_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://vswarm_sim/meshes/light_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="light_joint"
    type="fixed">
    <origin
      xyz="0 0 0"
      rpy="0 0 0" />
    <parent
      link="base_link" />
    <child
      link="light_link" />
    <axis
      xyz="0 0 0" />
  </joint>
  <gazebo reference="base_link">
        <material>Gazebo/Black</material>
  </gazebo>
  <gazebo reference="light_link">
        <material>Gazebo/White</material>
  </gazebo>
  <gazebo reference="lower_link">
        <material>Gazebo/White</material>
  </gazebo>
  <gazebo reference="upper_link">
        <material>Gazebo/Black</material>
  </gazebo>
  
</robot>
