<?xml version='1.0'?>
<sdf version='1.6'>
  <model name='bookshelf_large'>
    <link name='link_1'>
      <pose frame=''>-0.000121 -2.1e-05 0 0 -0 0</pose>
         <visual name='visual'>
        <pose frame=''>0 0 0 0 -0 0</pose>
        <geometry>
          <mesh>
            <uri>model://bookshelf_large/meshes/frame.dae</uri>
            <scale>1 1 1</scale>
          </mesh>
        </geometry>
       	 <material>
          <script>
            <uri>model://bookshelf_large/materials/scripts</uri>
            <uri>model://bookshelf_large/materials/textures</uri>
            <name>bookshelf</name>
          </script>
        </material>
        <cast_shadows>1</cast_shadows>
        <transparency>0</transparency>
      </visual>
      <collision name='collision'>
        <laser_retro>0</laser_retro>
        <max_contacts>10</max_contacts>
        <pose frame=''>0 0 0 0 -0 0</pose>
        <geometry>
          <mesh>
            <uri>model://bookshelf_large/meshes/frame.dae</uri>
            <scale>1 1 1</scale>
          </mesh>
        </geometry>
          </collision>
    </link>
    <link name='link_2'>
      <pose frame=''>0.000122 2.1e-05 0 0 -0 0</pose>
        <visual name='visual'>
        <pose frame=''>0 0 0 0 -0 0</pose>
        <geometry>
          <mesh>
            <uri>model://bookshelf_large/meshes/glass.dae</uri>
            <scale>1 1 1</scale>
          </mesh>
        </geometry>
        <material>
          <lighting>1</lighting>
             <ambient>0.3 0.7 0.8 1</ambient>
          <diffuse>0.3 0.9 0.9 1</diffuse>
          <specular>0.01 0.01 0.01 1</specular>
          <emissive>0 0 0 1</emissive>
        </material>
        <cast_shadows>1</cast_shadows>
        <transparency>0.5</transparency>
      </visual>
      <collision name='collision'>
        <laser_retro>0</laser_retro>
        <max_contacts>10</max_contacts>
        <pose frame=''>0 0 0 0 -0 0</pose>
        <geometry>
          <mesh>
            <uri>model://bookshelf_large/meshes/glass.dae</uri>
            <scale>1 1 1</scale>
          </mesh>
        </geometry>
      </collision>
    </link>
    <static>1</static>
    <allow_auto_disable>1</allow_auto_disable>
  </model>
</sdf>
