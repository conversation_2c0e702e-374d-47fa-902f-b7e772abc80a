<sdf version='1.7'>
  <world name='default'>
    <light name='sun' type='directional'>
      <cast_shadows>1</cast_shadows>
      <pose>0 0 10 0 -0 0</pose>
      <diffuse>0.8 0.8 0.8 1</diffuse>
      <specular>0.2 0.2 0.2 1</specular>
      <attenuation>
        <range>1000</range>
        <constant>0.9</constant>
        <linear>0.01</linear>
        <quadratic>0.001</quadratic>
      </attenuation>
      <direction>-0.5 0.1 -0.9</direction>
      <spot>
        <inner_angle>0</inner_angle>
        <outer_angle>0</outer_angle>
        <falloff>0</falloff>
      </spot>
    </light>
    <model name='ground_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <plane>
              <normal>0 0 1</normal>
              <size>7.5 8.6</size>
            </plane>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>100</mu>
                <mu2>50</mu2>
              </ode>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <contact>
              <ode/>
            </contact>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <plane>
              <normal>0 0 1</normal>
              <size>7.5 8.6</size>
            </plane>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <enable_wind>0</enable_wind>
      </link>
    </model>
    <physics name='default_physics' default='0' type='ode'>
      <ode>
        <solver>
          <type>quick</type>
          <iters>10</iters>
          <sor>1.3</sor>
          <use_dynamic_moi_rescaling>0</use_dynamic_moi_rescaling>
        </solver>
        <constraints>
          <cfm>0</cfm>
          <erp>0.2</erp>
          <contact_max_correcting_vel>100</contact_max_correcting_vel>
          <contact_surface_layer>0.001</contact_surface_layer>
        </constraints>
      </ode>
      <max_step_size>0.004</max_step_size>
      <real_time_factor>1</real_time_factor>
      <real_time_update_rate>250</real_time_update_rate>
    </physics>
    <gravity>0 0 -9.8066</gravity>
    <magnetic_field>6e-06 2.3e-05 -4.2e-05</magnetic_field>
    <atmosphere type='adiabatic'/>
    <scene>
      <ambient>0.4 0.4 0.4 1</ambient>
      <background>0.7 0.7 0.7 1</background>
      <shadows>1</shadows>
    </scene>
    <wind/>
    <spherical_coordinates>
      <surface_model>EARTH_WGS84</surface_model>
      <latitude_deg>0</latitude_deg>
      <longitude_deg>0</longitude_deg>
      <elevation>0</elevation>
      <heading_deg>0</heading_deg>
    </spherical_coordinates>
    <state world_name='default'>
      <sim_time>148 320000000</sim_time>
      <real_time>8 833143083</real_time>
      <wall_time>1694062586 460412956</wall_time>
      <iterations>2195</iterations>
      <model name='Floor_room'>
        <pose>0.043797 -0.01325 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='main'>
          <pose>0.043797 -0.01325 0.01 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='bookshelf_large_0'>
        <pose>-3.67 -4 0 0 -0 1.5708</pose>
        <scale>1 1 1</scale>
        <link name='link_1'>
          <pose>-3.66998 -4.00012 0 0 -0 1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='link_2'>
          <pose>-3.67002 -3.99988 0 0 -0 1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='bookshelf_large_1'>
        <pose>-3.67 -2.3 0 0 -0 1.5708</pose>
        <scale>1 1 1</scale>
        <link name='link_1'>
          <pose>-3.66998 -2.30012 0 0 -0 1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='link_2'>
          <pose>-3.67002 -2.29988 0 0 -0 1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='bookshelf_large_2'>
        <pose>-3.67 -0.6 0 0 -0 1.5708</pose>
        <scale>1 1 1</scale>
        <link name='link_1'>
          <pose>-3.66998 -0.600121 0 0 -0 1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='link_2'>
          <pose>-3.67002 -0.599878 0 0 -0 1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='bookshelf_large_3'>
        <pose>-3.67 1.1 0 0 -0 1.5708</pose>
        <scale>1 1 1</scale>
        <link name='link_1'>
          <pose>-3.66998 1.09988 0 0 -0 1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='link_2'>
          <pose>-3.67002 1.10012 0 0 -0 1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='bookshelf_large_4'>
        <pose>-3.67 2.8 0 0 -0 1.5708</pose>
        <scale>1 1 1</scale>
        <link name='link_1'>
          <pose>-3.66998 2.79988 0 0 -0 1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='link_2'>
          <pose>-3.67002 2.80012 0 0 -0 1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='bookshelf_large_5'>
        <pose>-2.6 4.7 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link_1'>
          <pose>-2.60012 4.69998 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='link_2'>
          <pose>-2.59987 4.70002 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='bookshelf_large_6'>
        <pose>-0.9 4.7 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link_1'>
          <pose>-0.900121 4.69998 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='link_2'>
          <pose>-0.899881 4.70003 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='bookshelf_large_7'>
        <pose>0.8 4.7 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link_1'>
          <pose>0.799879 4.69998 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='link_2'>
          <pose>0.800122 4.70002 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='bookshelf_large_8'>
        <pose>2.5 4.7 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link_1'>
          <pose>2.49988 4.69998 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='link_2'>
          <pose>2.50013 4.70002 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='drawer_white_1'>
        <pose>1.6 -4.4 0.02 0 0 -1.5708</pose>
        <scale>1 1 1</scale>
        <link name='link_0'>
          <pose>1.5798 -4.39571 0.02 0 0 -1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='link_1'>
          <pose>1.6202 -4.40429 0.02 0 0 -1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='drawer_white_3'>
        <pose>0.85 -4.4 0.02 0 0 -1.5708</pose>
        <scale>1 1 1</scale>
        <link name='link_0'>
          <pose>0.829798 -4.39571 0.02 0 0 -1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='link_1'>
          <pose>0.8702 -4.40428 0.02 0 0 -1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='ground_plane'>
        <pose>0 0 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>0 0 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='room'>
        <pose>-0.037777 0.006931 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='Wall_0'>
          <pose>-4.03778 0.006931 2 0 0 -1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_1'>
          <pose>3.96222 0.006931 2 0 0 -1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_3'>
          <pose>-0.037777 -5.06807 0.516113 0 -0 3.14159</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_4'>
          <pose>-0.037777 -5.06807 4 0 -0 3.14159</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_5'>
          <pose>-0.037777 5.08193 2 0 -0 3.14159</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='link_0'>
          <pose>-0.037777 0.006931 0.01 0 0 -1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box'>
        <pose>-1.75 0 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-1.75 0 0.31 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_0'>
        <pose>0.75 2 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>0.75 2 0.31 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_1'>
        <pose>1.75 -2 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>1.75 -2 0.31 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='window'>
        <pose>3.62277 -4.97689 1.7565 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link_2'>
          <pose>-0.03309 -4.99789 2.11803 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <light name='sun'>
        <pose>0 0 10 0 -0 0</pose>
      </light>
    </state>
    <gui fullscreen='0'>
      <camera name='user_camera'>
        <pose>-14.0905 3.96288 12.5792 0 0.469796 -0.4558</pose>
        <view_controller>orbit</view_controller>
        <projection_type>perspective</projection_type>
      </camera>
    </gui>
    <model name='Floor_room'>
      <link name='main'>
        <pose>0 0 0.01 0 -0 0</pose>
        <collision name='main_Collision'>
          <geometry>
            <box>
              <size>20 20 0.001</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='main_Visual'>
          <geometry>
            <box>
              <size>20 20 0.001</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>Floor_room/materials/scripts</uri>
              <uri>Floor_room/materials/textures</uri>
              <name>floor_room</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <static>1</static>
      <pose>0.043797 -0.01325 0 0 -0 0</pose>
    </model>
    <model name='window'>
      <link name='link_2'>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
        <pose>-3.65586 -0.020997 0.361529 0 -0 0</pose>
        <gravity>1</gravity>
        <inertial>
          <mass>1</mass>
          <pose>0 0 0 0 -0 0</pose>
          <inertia>
            <ixx>1</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>1</iyy>
            <iyz>0</iyz>
            <izz>1</izz>
          </inertia>
        </inertial>
        <visual name='visual'>
          <pose>0 0 0.9 0 -0 0</pose>
          <geometry>
            <box>
              <size>8 0.15 2</size>
            </box>
          </geometry>
          <material>
            <lighting>1</lighting>
            <ambient>0.1 0.5 0.6 1</ambient>
            <diffuse>0.1 0.5 0.6 1</diffuse>
            <specular>0.01 0.01 0.01 1</specular>
            <emissive>0 0 0 1</emissive>
            <shader type='pixel'>
              <normal_map>__default__</normal_map>
            </shader>
            <script>
              <name>ModelPreview_13::link_2::visual_MATERIAL_</name>
              <uri>__default__</uri>
            </script>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0.7</transparency>
        </visual>
        <collision name='collision'>
          <laser_retro>0</laser_retro>
          <max_contacts>10</max_contacts>
          <pose>0 0 0.9 0 -0 0</pose>
          <geometry>
            <box>
              <size>8 0.15 2</size>
            </box>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>1</mu>
                <mu2>1</mu2>
                <fdir1>0 0 0</fdir1>
                <slip1>0</slip1>
                <slip2>0</slip2>
              </ode>
              <torsional>
                <coefficient>1</coefficient>
                <patch_radius>0</patch_radius>
                <surface_radius>0</surface_radius>
                <use_patch_radius>1</use_patch_radius>
                <ode>
                  <slip>0</slip>
                </ode>
              </torsional>
            </friction>
            <bounce>
              <restitution_coefficient>0</restitution_coefficient>
              <threshold>1e+06</threshold>
            </bounce>
            <contact>
              <collide_without_contact>0</collide_without_contact>
              <collide_without_contact_bitmask>1</collide_without_contact_bitmask>
              <collide_bitmask>1</collide_bitmask>
              <ode>
                <soft_cfm>0</soft_cfm>
                <soft_erp>0.2</soft_erp>
                <kp>1e+13</kp>
                <kd>1</kd>
                <max_vel>0.01</max_vel>
                <min_depth>0</min_depth>
              </ode>
              <bullet>
                <split_impulse>1</split_impulse>
                <split_impulse_penetration_threshold>-0.01</split_impulse_penetration_threshold>
                <soft_cfm>0</soft_cfm>
                <soft_erp>0.2</soft_erp>
                <kp>1e+13</kp>
                <kd>1</kd>
              </bullet>
            </contact>
          </surface>
        </collision>
      </link>
      <static>1</static>
      <allow_auto_disable>1</allow_auto_disable>
      <pose>3.62277 -4.97689 1.7565 0 -0 0</pose>
    </model>
    <model name='drawer_white_1'>
      <link name='link_0'>
        <pose>-0.004287 -0.020202 0 0 -0 0</pose>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>drawer_white/meshes/base.dae</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>drawer_white/materials/scripts</uri>
              <uri>drawer_white/materials/textures</uri>
              <name>drawer_white</name>
            </script>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0</transparency>
        </visual>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>drawer_white/meshes/base.dae</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
          <material>
            <script>
              <uri>drawer_white/materials/scripts</uri>
              <uri>drawer_white/materials/textures</uri>
              <name>drawer_white</name>
            </script>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0</transparency>
        </collision>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='link_1'>
        <pose>0.004286 0.020202 0 0 -0 0</pose>
        <visual name='visual'>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>drawer_white/meshes/handle.dae</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <material>
            <lighting>1</lighting>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>0.3 0.3 0.3 1</ambient>
            <diffuse>0.7 0.7 0.7 1</diffuse>
            <specular>0.01 0.01 0.01 1</specular>
            <emissive>0 0 0 1</emissive>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0</transparency>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <static>1</static>
      <allow_auto_disable>1</allow_auto_disable>
      <pose>-0.816379 -4.15734 0.020462 0 0 -0.017089</pose>
    </model>
    <model name='drawer_white_3'>
      <link name='link_0'>
        <pose>-0.004287 -0.020202 0 0 -0 0</pose>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>drawer_white/meshes/base.dae</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>drawer_white/materials/scripts</uri>
              <uri>drawer_white/materials/textures</uri>
              <name>drawer_white</name>
            </script>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0</transparency>
        </visual>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>drawer_white/meshes/base.dae</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
          <material>
            <script>
              <uri>drawer_white/materials/scripts</uri>
              <uri>drawer_white/materials/textures</uri>
              <name>drawer_white</name>
            </script>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0</transparency>
        </collision>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='link_1'>
        <pose>0.004286 0.020202 0 0 -0 0</pose>
        <visual name='visual'>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>drawer_white/meshes/handle.dae</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <material>
            <lighting>1</lighting>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>0.3 0.3 0.3 1</ambient>
            <diffuse>0.7 0.7 0.7 1</diffuse>
            <specular>0.01 0.01 0.01 1</specular>
            <emissive>0 0 0 1</emissive>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0</transparency>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <static>1</static>
      <allow_auto_disable>1</allow_auto_disable>
      <pose>2.63285 -4.48253 0.020462 0 0 -0.017089</pose>
    </model>
    <model name='bookshelf_large_1'>
      <link name='link_1'>
        <pose>-0.000121 -2.1e-05 0 0 -0 0</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
        <inertial>
          <mass>1</mass>
          <pose>0 0 0 0 -0 0</pose>
          <inertia>
            <ixx>1</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>1</iyy>
            <iyz>0</iyz>
            <izz>1</izz>
          </inertia>
        </inertial>
        <visual name='visual'>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>bookshelf_large/meshes/frame.dae</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>bookshelf_large/materials/scripts</uri>
              <uri>bookshelf_large/materials/textures</uri>
              <name>Gazebo/White</name>
            </script>
            <shader type='pixel'>
              <normal_map>__default__</normal_map>
            </shader>
            <ambient>1 1 1 1</ambient>
            <diffuse>1 1 1 1</diffuse>
            <specular>0 0 0 1</specular>
            <emissive>0 0 0 1</emissive>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0</transparency>
        </visual>
        <collision name='collision'>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>bookshelf_large/meshes/frame.dae</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
      </link>
      <link name='link_2'>
        <pose>0.000122 2.1e-05 0 0 -0 0</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
        <visual name='visual'>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>bookshelf_large/meshes/glass.dae</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <material>
            <lighting>1</lighting>
            <ambient>0.3 0.7 0.8 1</ambient>
            <diffuse>0.3 0.9 0.9 1</diffuse>
            <specular>0.01 0.01 0.01 1</specular>
            <emissive>0 0 0 1</emissive>
            <shader type='pixel'/>
            <script>
              <name>ModelPreview_29::link_2::visual_MATERIAL_</name>
              <uri>__default__</uri>
            </script>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0.5</transparency>
        </visual>
      </link>
      <static>1</static>
      <allow_auto_disable>1</allow_auto_disable>
      <pose>-2.77508 -2.31133 0 0 -0 1.5708</pose>
    </model>
    <model name='bookshelf_large_2'>
      <link name='link_1'>
        <pose>-0.000121 -2.1e-05 0 0 -0 0</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
        <inertial>
          <mass>1</mass>
          <pose>0 0 0 0 -0 0</pose>
          <inertia>
            <ixx>1</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>1</iyy>
            <iyz>0</iyz>
            <izz>1</izz>
          </inertia>
        </inertial>
        <visual name='visual'>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>bookshelf_large/meshes/frame.dae</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>bookshelf_large/materials/scripts</uri>
              <uri>bookshelf_large/materials/textures</uri>
              <name>Gazebo/White</name>
            </script>
            <shader type='pixel'>
              <normal_map>__default__</normal_map>
            </shader>
            <ambient>1 1 1 1</ambient>
            <diffuse>1 1 1 1</diffuse>
            <specular>0 0 0 1</specular>
            <emissive>0 0 0 1</emissive>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0</transparency>
        </visual>
        <collision name='collision'>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>bookshelf_large/meshes/frame.dae</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
      </link>
      <link name='link_2'>
        <pose>0.000122 2.1e-05 0 0 -0 0</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
        <visual name='visual'>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>bookshelf_large/meshes/glass.dae</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <material>
            <lighting>1</lighting>
            <ambient>0.3 0.7 0.8 1</ambient>
            <diffuse>0.3 0.9 0.9 1</diffuse>
            <specular>0.01 0.01 0.01 1</specular>
            <emissive>0 0 0 1</emissive>
            <shader type='pixel'/>
            <script>
              <name>ModelPreview_29::link_2::visual_MATERIAL_</name>
              <uri>__default__</uri>
            </script>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0.5</transparency>
        </visual>
        <collision name='collision'>
          <laser_retro>0</laser_retro>
          <max_contacts>10</max_contacts>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>bookshelf_large/meshes/glass.dae</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>1</mu>
                <mu2>1</mu2>
                <fdir1>0 0 0</fdir1>
                <slip1>0</slip1>
                <slip2>0</slip2>
              </ode>
              <torsional>
                <coefficient>1</coefficient>
                <patch_radius>0</patch_radius>
                <surface_radius>0</surface_radius>
                <use_patch_radius>1</use_patch_radius>
                <ode>
                  <slip>0</slip>
                </ode>
              </torsional>
            </friction>
            <bounce>
              <restitution_coefficient>0</restitution_coefficient>
              <threshold>1e+06</threshold>
            </bounce>
            <contact>
              <collide_without_contact>0</collide_without_contact>
              <collide_without_contact_bitmask>1</collide_without_contact_bitmask>
              <collide_bitmask>1</collide_bitmask>
              <ode>
                <soft_cfm>0</soft_cfm>
                <soft_erp>0.2</soft_erp>
                <kp>1e+13</kp>
                <kd>1</kd>
                <max_vel>0.01</max_vel>
                <min_depth>0</min_depth>
              </ode>
              <bullet>
                <split_impulse>1</split_impulse>
                <split_impulse_penetration_threshold>-0.01</split_impulse_penetration_threshold>
                <soft_cfm>0</soft_cfm>
                <soft_erp>0.2</soft_erp>
                <kp>1e+13</kp>
                <kd>1</kd>
              </bullet>
            </contact>
          </surface>
        </collision>
      </link>
      <static>1</static>
      <allow_auto_disable>1</allow_auto_disable>
      <pose>-3.116 -0.813456 0 0 -0 1.5708</pose>
    </model>
    <model name='bookshelf_large_3'>
      <link name='link_1'>
        <pose>-0.000121 -2.1e-05 0 0 -0 0</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
        <inertial>
          <mass>1</mass>
          <pose>0 0 0 0 -0 0</pose>
          <inertia>
            <ixx>1</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>1</iyy>
            <iyz>0</iyz>
            <izz>1</izz>
          </inertia>
        </inertial>
        <visual name='visual'>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>bookshelf_large/meshes/frame.dae</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>bookshelf_large/materials/scripts</uri>
              <uri>bookshelf_large/materials/textures</uri>
              <name>Gazebo/White</name>
            </script>
            <shader type='pixel'>
              <normal_map>__default__</normal_map>
            </shader>
            <ambient>1 1 1 1</ambient>
            <diffuse>1 1 1 1</diffuse>
            <specular>0 0 0 1</specular>
            <emissive>0 0 0 1</emissive>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0</transparency>
        </visual>
        <collision name='collision'>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>bookshelf_large/meshes/frame.dae</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
      </link>
      <link name='link_2'>
        <pose>0.000122 2.1e-05 0 0 -0 0</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
        <visual name='visual'>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>bookshelf_large/meshes/glass.dae</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <material>
            <lighting>1</lighting>
            <ambient>0.3 0.7 0.8 1</ambient>
            <diffuse>0.3 0.9 0.9 1</diffuse>
            <specular>0.01 0.01 0.01 1</specular>
            <emissive>0 0 0 1</emissive>
            <shader type='pixel'/>
            <script>
              <name>ModelPreview_29::link_2::visual_MATERIAL_</name>
              <uri>__default__</uri>
            </script>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0.5</transparency>
        </visual>
      </link>
      <static>1</static>
      <allow_auto_disable>1</allow_auto_disable>
      <pose>-2.35644 1.30649 0 0 -0 1.5708</pose>
    </model>
    <model name='bookshelf_large_5'>
      <link name='link_1'>
        <pose>-0.000121 -2.1e-05 0 0 -0 0</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
        <inertial>
          <mass>1</mass>
          <pose>0 0 0 0 -0 0</pose>
          <inertia>
            <ixx>1</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>1</iyy>
            <iyz>0</iyz>
            <izz>1</izz>
          </inertia>
        </inertial>
        <visual name='visual'>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>bookshelf_large/meshes/frame.dae</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>bookshelf_large/materials/scripts</uri>
              <uri>bookshelf_large/materials/textures</uri>
              <name>Gazebo/White</name>
            </script>
            <shader type='pixel'>
              <normal_map>__default__</normal_map>
            </shader>
            <ambient>1 1 1 1</ambient>
            <diffuse>1 1 1 1</diffuse>
            <specular>0 0 0 1</specular>
            <emissive>0 0 0 1</emissive>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0</transparency>
        </visual>
        <collision name='collision'>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>bookshelf_large/meshes/frame.dae</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
      </link>
      <link name='link_2'>
        <pose>0.000122 2.1e-05 0 0 -0 0</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
        <visual name='visual'>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>bookshelf_large/meshes/glass.dae</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <material>
            <lighting>1</lighting>
            <ambient>0.3 0.7 0.8 1</ambient>
            <diffuse>0.3 0.9 0.9 1</diffuse>
            <specular>0.01 0.01 0.01 1</specular>
            <emissive>0 0 0 1</emissive>
            <shader type='pixel'/>
            <script>
              <name>ModelPreview_29::link_2::visual_MATERIAL_</name>
              <uri>__default__</uri>
            </script>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0.5</transparency>
        </visual>
      </link>
      <static>1</static>
      <allow_auto_disable>1</allow_auto_disable>
      <pose>-2.22606 3.20216 0 0 -0 1.5708</pose>
    </model>
    <model name='bookshelf_large_4'>
      <link name='link_1'>
        <pose>-0.000121 -2.1e-05 0 0 -0 0</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
        <inertial>
          <mass>1</mass>
          <pose>0 0 0 0 -0 0</pose>
          <inertia>
            <ixx>1</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>1</iyy>
            <iyz>0</iyz>
            <izz>1</izz>
          </inertia>
        </inertial>
        <visual name='visual'>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>bookshelf_large/meshes/frame.dae</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>bookshelf_large/materials/scripts</uri>
              <uri>bookshelf_large/materials/textures</uri>
              <name>Gazebo/White</name>
            </script>
            <shader type='pixel'>
              <normal_map>__default__</normal_map>
            </shader>
            <ambient>1 1 1 1</ambient>
            <diffuse>1 1 1 1</diffuse>
            <specular>0 0 0 1</specular>
            <emissive>0 0 0 1</emissive>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0</transparency>
        </visual>
        <collision name='collision'>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>bookshelf_large/meshes/frame.dae</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
      </link>
      <link name='link_2'>
        <pose>0.000122 2.1e-05 0 0 -0 0</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
        <visual name='visual'>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>bookshelf_large/meshes/glass.dae</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <material>
            <lighting>1</lighting>
            <ambient>0.3 0.7 0.8 1</ambient>
            <diffuse>0.3 0.9 0.9 1</diffuse>
            <specular>0.01 0.01 0.01 1</specular>
            <emissive>0 0 0 1</emissive>
            <shader type='pixel'/>
            <script>
              <name>ModelPreview_29::link_2::visual_MATERIAL_</name>
              <uri>__default__</uri>
            </script>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0.5</transparency>
        </visual>
      </link>
      <static>1</static>
      <allow_auto_disable>1</allow_auto_disable>
      <pose>-2.55567 -3.43102 0 0 -0 1.5708</pose>
    </model>
    <model name='bookshelf_large_6'>
      <link name='link_1'>
        <pose>-0.000121 -2.1e-05 0 0 -0 0</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
        <inertial>
          <mass>1</mass>
          <pose>0 0 0 0 -0 0</pose>
          <inertia>
            <ixx>1</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>1</iyy>
            <iyz>0</iyz>
            <izz>1</izz>
          </inertia>
        </inertial>
        <visual name='visual'>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>bookshelf_large/meshes/frame.dae</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>bookshelf_large/materials/scripts</uri>
              <uri>bookshelf_large/materials/textures</uri>
              <name>Gazebo/White</name>
            </script>
            <shader type='pixel'>
              <normal_map>__default__</normal_map>
            </shader>
            <ambient>1 1 1 1</ambient>
            <diffuse>1 1 1 1</diffuse>
            <specular>0 0 0 1</specular>
            <emissive>0 0 0 1</emissive>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0</transparency>
        </visual>
        <collision name='collision'>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>bookshelf_large/meshes/frame.dae</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
      </link>
      <link name='link_2'>
        <pose>0.000122 2.1e-05 0 0 -0 0</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
        <visual name='visual'>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>bookshelf_large/meshes/glass.dae</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <material>
            <lighting>1</lighting>
            <ambient>0.3 0.7 0.8 1</ambient>
            <diffuse>0.3 0.9 0.9 1</diffuse>
            <specular>0.01 0.01 0.01 1</specular>
            <emissive>0 0 0 1</emissive>
            <shader type='pixel'/>
            <script>
              <name>ModelPreview_29::link_2::visual_MATERIAL_</name>
              <uri>__default__</uri>
            </script>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0.5</transparency>
        </visual>
      </link>
      <static>1</static>
      <allow_auto_disable>1</allow_auto_disable>
      <pose>-1.06215 2.8742 0 0 -0 1.5708</pose>
    </model>
    <model name='bookshelf_large_0'>
      <link name='link_1'>
        <pose>-0.000121 -2.1e-05 0 0 -0 0</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
        <inertial>
          <mass>1</mass>
          <pose>0 0 0 0 -0 0</pose>
          <inertia>
            <ixx>1</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>1</iyy>
            <iyz>0</iyz>
            <izz>1</izz>
          </inertia>
        </inertial>
        <visual name='visual'>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>bookshelf_large/meshes/frame.dae</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>bookshelf_large/materials/scripts</uri>
              <uri>bookshelf_large/materials/textures</uri>
              <name>Gazebo/White</name>
            </script>
            <shader type='pixel'>
              <normal_map>__default__</normal_map>
            </shader>
            <ambient>1 1 1 1</ambient>
            <diffuse>1 1 1 1</diffuse>
            <specular>0 0 0 1</specular>
            <emissive>0 0 0 1</emissive>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0</transparency>
        </visual>
        <collision name='collision'>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>bookshelf_large/meshes/frame.dae</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
      </link>
      <link name='link_2'>
        <pose>0.000122 2.1e-05 0 0 -0 0</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
        <visual name='visual'>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>bookshelf_large/meshes/glass.dae</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <material>
            <lighting>1</lighting>
            <ambient>0.3 0.7 0.8 1</ambient>
            <diffuse>0.3 0.9 0.9 1</diffuse>
            <specular>0.01 0.01 0.01 1</specular>
            <emissive>0 0 0 1</emissive>
            <shader type='pixel'/>
            <script>
              <name>ModelPreview_29::link_2::visual_MATERIAL_</name>
              <uri>__default__</uri>
            </script>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0.5</transparency>
        </visual>
      </link>
      <static>1</static>
      <allow_auto_disable>1</allow_auto_disable>
      <pose>-0.642577 3.64835 0 0 -0 4e-06</pose>
    </model>
    <model name='bookshelf_large_7'>
      <link name='link_1'>
        <pose>-0.000121 -2.1e-05 0 0 -0 0</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
        <inertial>
          <mass>1</mass>
          <pose>0 0 0 0 -0 0</pose>
          <inertia>
            <ixx>1</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>1</iyy>
            <iyz>0</iyz>
            <izz>1</izz>
          </inertia>
        </inertial>
        <visual name='visual'>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>bookshelf_large/meshes/frame.dae</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>bookshelf_large/materials/scripts</uri>
              <uri>bookshelf_large/materials/textures</uri>
              <name>Gazebo/White</name>
            </script>
            <shader type='pixel'>
              <normal_map>__default__</normal_map>
            </shader>
            <ambient>1 1 1 1</ambient>
            <diffuse>1 1 1 1</diffuse>
            <specular>0 0 0 1</specular>
            <emissive>0 0 0 1</emissive>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0</transparency>
        </visual>
        <collision name='collision'>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>bookshelf_large/meshes/frame.dae</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
      </link>
      <link name='link_2'>
        <pose>0.000122 2.1e-05 0 0 -0 0</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
        <visual name='visual'>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>bookshelf_large/meshes/glass.dae</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <material>
            <lighting>1</lighting>
            <ambient>0.3 0.7 0.8 1</ambient>
            <diffuse>0.3 0.9 0.9 1</diffuse>
            <specular>0.01 0.01 0.01 1</specular>
            <emissive>0 0 0 1</emissive>
            <shader type='pixel'/>
            <script>
              <name>ModelPreview_29::link_2::visual_MATERIAL_</name>
              <uri>__default__</uri>
            </script>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0.5</transparency>
        </visual>
      </link>
      <static>1</static>
      <allow_auto_disable>1</allow_auto_disable>
      <pose>0.372529 2.24806 0 0 -0 4e-06</pose>
    </model>
    <model name='bookshelf_large_8'>
      <link name='link_1'>
        <pose>-0.000121 -2.1e-05 0 0 -0 0</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
        <inertial>
          <mass>1</mass>
          <pose>0 0 0 0 -0 0</pose>
          <inertia>
            <ixx>1</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>1</iyy>
            <iyz>0</iyz>
            <izz>1</izz>
          </inertia>
        </inertial>
        <visual name='visual'>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>bookshelf_large/meshes/frame.dae</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>bookshelf_large/materials/scripts</uri>
              <uri>bookshelf_large/materials/textures</uri>
              <name>Gazebo/White</name>
            </script>
            <shader type='pixel'>
              <normal_map>__default__</normal_map>
            </shader>
            <ambient>1 1 1 1</ambient>
            <diffuse>1 1 1 1</diffuse>
            <specular>0 0 0 1</specular>
            <emissive>0 0 0 1</emissive>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0</transparency>
        </visual>
        <collision name='collision'>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>bookshelf_large/meshes/frame.dae</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
      </link>
      <link name='link_2'>
        <pose>0.000122 2.1e-05 0 0 -0 0</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
        <visual name='visual'>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>bookshelf_large/meshes/glass.dae</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <material>
            <lighting>1</lighting>
            <ambient>0.3 0.7 0.8 1</ambient>
            <diffuse>0.3 0.9 0.9 1</diffuse>
            <specular>0.01 0.01 0.01 1</specular>
            <emissive>0 0 0 1</emissive>
            <shader type='pixel'/>
            <script>
              <name>ModelPreview_29::link_2::visual_MATERIAL_</name>
              <uri>__default__</uri>
            </script>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0.5</transparency>
        </visual>
      </link>
      <static>1</static>
      <allow_auto_disable>1</allow_auto_disable>
      <pose>1.04226 2.84767 0 0 -0 4e-06</pose>
    </model>
    <model name='room'>
      <link name='Wall_0'>
        <pose>-4 0 2 0 0 -1.5708</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
        <inertial>
          <mass>1</mass>
          <pose>0 0 0 0 -0 0</pose>
          <inertia>
            <ixx>1</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>1</iyy>
            <iyz>0</iyz>
            <izz>1</izz>
          </inertia>
        </inertial>
        <visual name='Wall_0_Visual'>
          <pose>0 0 0.5 0 -0 0</pose>
          <geometry>
            <box>
              <size>10 0.15 5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/White</name>
            </script>
            <ambient>1 1 1 1</ambient>
            <shader type='pixel'>
              <normal_map>__default__</normal_map>
            </shader>
            <diffuse>0.7 0.7 0.7 1</diffuse>
            <specular>0.01 0.01 0.01 1</specular>
            <emissive>0 0 0 1</emissive>
          </material>
          <transparency>0</transparency>
          <cast_shadows>1</cast_shadows>
        </visual>
        <collision name='Wall_0_Collision'>
          <laser_retro>0</laser_retro>
          <max_contacts>10</max_contacts>
          <pose>0 0 0.5 0 -0 0</pose>
          <geometry>
            <box>
              <size>10 0.15 5</size>
            </box>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>1</mu>
                <mu2>1</mu2>
                <fdir1>0 0 0</fdir1>
                <slip1>0</slip1>
                <slip2>0</slip2>
              </ode>
              <torsional>
                <coefficient>1</coefficient>
                <patch_radius>0</patch_radius>
                <surface_radius>0</surface_radius>
                <use_patch_radius>1</use_patch_radius>
                <ode>
                  <slip>0</slip>
                </ode>
              </torsional>
            </friction>
            <bounce>
              <restitution_coefficient>0</restitution_coefficient>
              <threshold>1e+06</threshold>
            </bounce>
            <contact>
              <collide_without_contact>0</collide_without_contact>
              <collide_without_contact_bitmask>1</collide_without_contact_bitmask>
              <collide_bitmask>1</collide_bitmask>
              <ode>
                <soft_cfm>0</soft_cfm>
                <soft_erp>0.2</soft_erp>
                <kp>1e+13</kp>
                <kd>1</kd>
                <max_vel>0.01</max_vel>
                <min_depth>0</min_depth>
              </ode>
              <bullet>
                <split_impulse>1</split_impulse>
                <split_impulse_penetration_threshold>-0.01</split_impulse_penetration_threshold>
                <soft_cfm>0</soft_cfm>
                <soft_erp>0.2</soft_erp>
                <kp>1e+13</kp>
                <kd>1</kd>
              </bullet>
            </contact>
          </surface>
        </collision>
      </link>
      <link name='Wall_1'>
        <pose>4 0 2 0 0 -1.5708</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
        <inertial>
          <mass>1</mass>
          <pose>0 0 0 0 -0 0</pose>
          <inertia>
            <ixx>1</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>1</iyy>
            <iyz>0</iyz>
            <izz>1</izz>
          </inertia>
        </inertial>
        <visual name='Wall_0_Visual'>
          <pose>0 0 0.5 0 -0 0</pose>
          <geometry>
            <box>
              <size>10 0.15 5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/White</name>
            </script>
            <ambient>1 1 1 1</ambient>
            <shader type='pixel'>
              <normal_map>__default__</normal_map>
            </shader>
            <diffuse>0.7 0.7 0.7 1</diffuse>
            <specular>0.01 0.01 0.01 1</specular>
            <emissive>0 0 0 1</emissive>
          </material>
          <transparency>0</transparency>
          <cast_shadows>1</cast_shadows>
        </visual>
        <collision name='Wall_0_Collision'>
          <laser_retro>0</laser_retro>
          <max_contacts>10</max_contacts>
          <pose>0 0 0.5 0 -0 0</pose>
          <geometry>
            <box>
              <size>10 0.15 5</size>
            </box>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>1</mu>
                <mu2>1</mu2>
                <fdir1>0 0 0</fdir1>
                <slip1>0</slip1>
                <slip2>0</slip2>
              </ode>
              <torsional>
                <coefficient>1</coefficient>
                <patch_radius>0</patch_radius>
                <surface_radius>0</surface_radius>
                <use_patch_radius>1</use_patch_radius>
                <ode>
                  <slip>0</slip>
                </ode>
              </torsional>
            </friction>
            <bounce>
              <restitution_coefficient>0</restitution_coefficient>
              <threshold>1e+06</threshold>
            </bounce>
            <contact>
              <collide_without_contact>0</collide_without_contact>
              <collide_without_contact_bitmask>1</collide_without_contact_bitmask>
              <collide_bitmask>1</collide_bitmask>
              <ode>
                <soft_cfm>0</soft_cfm>
                <soft_erp>0.2</soft_erp>
                <kp>1e+13</kp>
                <kd>1</kd>
                <max_vel>0.01</max_vel>
                <min_depth>0</min_depth>
              </ode>
              <bullet>
                <split_impulse>1</split_impulse>
                <split_impulse_penetration_threshold>-0.01</split_impulse_penetration_threshold>
                <soft_cfm>0</soft_cfm>
                <soft_erp>0.2</soft_erp>
                <kp>1e+13</kp>
                <kd>1</kd>
              </bullet>
            </contact>
          </surface>
        </collision>
      </link>
      <link name='Wall_3'>
        <pose>0 -5.075 0.5 0 -0 3.14159</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
        <inertial>
          <inertia>
            <ixx>0</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0</iyy>
            <iyz>0</iyz>
            <izz>0</izz>
          </inertia>
          <mass>0</mass>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <gravity>1</gravity>
        <visual name='Wall_3_Visual'>
          <pose>0 0 0.5 0 -0 0</pose>
          <geometry>
            <box>
              <size>8.15 0.15 2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
            <shader type='pixel'>
              <normal_map>__default__</normal_map>
            </shader>
            <emissive>0 0 0 1</emissive>
            <diffuse>0.7 0.7 0.7 1</diffuse>
            <specular>0.01 0.01 0.01 1</specular>
          </material>
          <transparency>0</transparency>
          <cast_shadows>1</cast_shadows>
        </visual>
        <collision name='Wall_3_Collision'>
          <laser_retro>0</laser_retro>
          <max_contacts>10</max_contacts>
          <pose>0 0 0.5 0 -0 0</pose>
          <geometry>
            <box>
              <size>8.15 0.15 2</size>
            </box>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>1</mu>
                <mu2>1</mu2>
                <fdir1>0 0 0</fdir1>
                <slip1>0</slip1>
                <slip2>0</slip2>
              </ode>
              <torsional>
                <coefficient>1</coefficient>
                <patch_radius>0</patch_radius>
                <surface_radius>0</surface_radius>
                <use_patch_radius>1</use_patch_radius>
                <ode>
                  <slip>0</slip>
                </ode>
              </torsional>
            </friction>
            <bounce>
              <restitution_coefficient>0</restitution_coefficient>
              <threshold>1e+06</threshold>
            </bounce>
            <contact>
              <collide_without_contact>0</collide_without_contact>
              <collide_without_contact_bitmask>1</collide_without_contact_bitmask>
              <collide_bitmask>1</collide_bitmask>
              <ode>
                <soft_cfm>0</soft_cfm>
                <soft_erp>0.2</soft_erp>
                <kp>1e+13</kp>
                <kd>1</kd>
                <max_vel>0.01</max_vel>
                <min_depth>0</min_depth>
              </ode>
              <bullet>
                <split_impulse>1</split_impulse>
                <split_impulse_penetration_threshold>-0.01</split_impulse_penetration_threshold>
                <soft_cfm>0</soft_cfm>
                <soft_erp>0.2</soft_erp>
                <kp>1e+13</kp>
                <kd>1</kd>
              </bullet>
            </contact>
          </surface>
        </collision>
      </link>
      <link name='Wall_4'>
        <pose>0 -5.075 4 0 -0 3.14159</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
        <inertial>
          <inertia>
            <ixx>0</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0</iyy>
            <iyz>0</iyz>
            <izz>0</izz>
          </inertia>
          <mass>0</mass>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <gravity>1</gravity>
        <visual name='Wall_3_Visual'>
          <pose>0 0 0.5 0 -0 0</pose>
          <geometry>
            <box>
              <size>8.15 0.15 1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
            <shader type='pixel'>
              <normal_map>__default__</normal_map>
            </shader>
            <emissive>0 0 0 1</emissive>
            <diffuse>0.7 0.7 0.7 1</diffuse>
            <specular>0.01 0.01 0.01 1</specular>
          </material>
          <transparency>0</transparency>
          <cast_shadows>1</cast_shadows>
        </visual>
        <collision name='Wall_3_Collision'>
          <laser_retro>0</laser_retro>
          <max_contacts>10</max_contacts>
          <pose>0 0 0.5 0 -0 0</pose>
          <geometry>
            <box>
              <size>8.15 0.15 1</size>
            </box>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>1</mu>
                <mu2>1</mu2>
                <fdir1>0 0 0</fdir1>
                <slip1>0</slip1>
                <slip2>0</slip2>
              </ode>
              <torsional>
                <coefficient>1</coefficient>
                <patch_radius>0</patch_radius>
                <surface_radius>0</surface_radius>
                <use_patch_radius>1</use_patch_radius>
                <ode>
                  <slip>0</slip>
                </ode>
              </torsional>
            </friction>
            <bounce>
              <restitution_coefficient>0</restitution_coefficient>
              <threshold>1e+06</threshold>
            </bounce>
            <contact>
              <collide_without_contact>0</collide_without_contact>
              <collide_without_contact_bitmask>1</collide_without_contact_bitmask>
              <collide_bitmask>1</collide_bitmask>
              <ode>
                <soft_cfm>0</soft_cfm>
                <soft_erp>0.2</soft_erp>
                <kp>1e+13</kp>
                <kd>1</kd>
                <max_vel>0.01</max_vel>
                <min_depth>0</min_depth>
              </ode>
              <bullet>
                <split_impulse>1</split_impulse>
                <split_impulse_penetration_threshold>-0.01</split_impulse_penetration_threshold>
                <soft_cfm>0</soft_cfm>
                <soft_erp>0.2</soft_erp>
                <kp>1e+13</kp>
                <kd>1</kd>
              </bullet>
            </contact>
          </surface>
        </collision>
      </link>
      <link name='Wall_5'>
        <pose>0 5.075 2 0 -0 3.14159</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
        <inertial>
          <mass>1</mass>
          <pose>0 0 0 0 -0 0</pose>
          <inertia>
            <ixx>1</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>1</iyy>
            <iyz>0</iyz>
            <izz>1</izz>
          </inertia>
        </inertial>
        <visual name='Wall_5_Visual'>
          <pose>0 0 0.5 0 -0 0</pose>
          <geometry>
            <box>
              <size>8.15 0.15 5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
            <shader type='pixel'>
              <normal_map>__default__</normal_map>
            </shader>
            <diffuse>0.7 0.7 0.7 1</diffuse>
            <specular>0.01 0.01 0.01 1</specular>
            <emissive>0 0 0 1</emissive>
          </material>
          <transparency>0</transparency>
          <cast_shadows>1</cast_shadows>
        </visual>
        <collision name='Wall_5_Collision'>
          <laser_retro>0</laser_retro>
          <max_contacts>10</max_contacts>
          <pose>0 0 0.5 0 -0 0</pose>
          <geometry>
            <box>
              <size>8.15 0.15 5</size>
            </box>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>1</mu>
                <mu2>1</mu2>
                <fdir1>0 0 0</fdir1>
                <slip1>0</slip1>
                <slip2>0</slip2>
              </ode>
              <torsional>
                <coefficient>1</coefficient>
                <patch_radius>0</patch_radius>
                <surface_radius>0</surface_radius>
                <use_patch_radius>1</use_patch_radius>
                <ode>
                  <slip>0</slip>
                </ode>
              </torsional>
            </friction>
            <bounce>
              <restitution_coefficient>0</restitution_coefficient>
              <threshold>1e+06</threshold>
            </bounce>
            <contact>
              <collide_without_contact>0</collide_without_contact>
              <collide_without_contact_bitmask>1</collide_without_contact_bitmask>
              <collide_bitmask>1</collide_bitmask>
              <ode>
                <soft_cfm>0</soft_cfm>
                <soft_erp>0.2</soft_erp>
                <kp>1e+13</kp>
                <kd>1</kd>
                <max_vel>0.01</max_vel>
                <min_depth>0</min_depth>
              </ode>
              <bullet>
                <split_impulse>1</split_impulse>
                <split_impulse_penetration_threshold>-0.01</split_impulse_penetration_threshold>
                <soft_cfm>0</soft_cfm>
                <soft_erp>0.2</soft_erp>
                <kp>1e+13</kp>
                <kd>1</kd>
              </bullet>
            </contact>
          </surface>
        </collision>
      </link>
      <link name='link_0'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <pose>0 0 0.01 0 0 -1.5708</pose>
        <gravity>1</gravity>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <enable_wind>0</enable_wind>
        <visual name='visual'>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>10 8 0.013</size>
            </box>
          </geometry>
          <material>
            <lighting>1</lighting>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Blue</name>
            </script>
            <shader type='pixel'>
              <normal_map>__default__</normal_map>
            </shader>
            <ambient>0.1 0.5 0.6 1</ambient>
            <diffuse>0.1 0.5 0.6 1</diffuse>
            <specular>0.01 0.01 0.01 1</specular>
            <emissive>0 0 0 1</emissive>
          </material>
          <transparency>0</transparency>
          <cast_shadows>1</cast_shadows>
        </visual>
        <collision name='collision'>
          <laser_retro>0</laser_retro>
          <max_contacts>10</max_contacts>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.01 0.01 0.01</size>
            </box>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>1</mu>
                <mu2>1</mu2>
                <fdir1>0 0 0</fdir1>
                <slip1>0</slip1>
                <slip2>0</slip2>
              </ode>
              <torsional>
                <coefficient>1</coefficient>
                <patch_radius>0</patch_radius>
                <surface_radius>0</surface_radius>
                <use_patch_radius>1</use_patch_radius>
                <ode>
                  <slip>0</slip>
                </ode>
              </torsional>
            </friction>
            <bounce>
              <restitution_coefficient>0</restitution_coefficient>
              <threshold>1e+06</threshold>
            </bounce>
            <contact>
              <collide_without_contact>0</collide_without_contact>
              <collide_without_contact_bitmask>1</collide_without_contact_bitmask>
              <collide_bitmask>1</collide_bitmask>
              <ode>
                <soft_cfm>0</soft_cfm>
                <soft_erp>0.2</soft_erp>
                <kp>1e+13</kp>
                <kd>1</kd>
                <max_vel>0.01</max_vel>
                <min_depth>0</min_depth>
              </ode>
              <bullet>
                <split_impulse>1</split_impulse>
                <split_impulse_penetration_threshold>-0.01</split_impulse_penetration_threshold>
                <soft_cfm>0</soft_cfm>
                <soft_erp>0.2</soft_erp>
                <kp>1e+13</kp>
                <kd>1</kd>
              </bullet>
            </contact>
          </surface>
        </collision>
      </link>
      <static>1</static>
      <allow_auto_disable>1</allow_auto_disable>
      <pose>0 0 0 0 -0 0</pose>
    </model>
    <model name='unit_box'>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
        <pose>0 0 0.31 0 -0 0</pose>
        <gravity>1</gravity>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.6 0.6 0.6</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
            <shader type='pixel'>
              <normal_map>__default__</normal_map>
            </shader>
            <ambient>0.3 0.02 0 1</ambient>
            <diffuse>0.2 0.02 0 1</diffuse>
            <specular>0.01 0.01 0.01 1</specular>
            <emissive>0.3 0 0 1</emissive>
            <lighting>1</lighting>
          </material>
          <pose>0 0 0 0 -0 0</pose>
          <transparency>0</transparency>
          <cast_shadows>1</cast_shadows>
        </visual>
        <collision name='collision'>
          <laser_retro>0</laser_retro>
          <max_contacts>10</max_contacts>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.6 0.6 0.6</size>
            </box>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>1</mu>
                <mu2>1</mu2>
                <fdir1>0 0 0</fdir1>
                <slip1>0</slip1>
                <slip2>0</slip2>
              </ode>
              <torsional>
                <coefficient>1</coefficient>
                <patch_radius>0</patch_radius>
                <surface_radius>0</surface_radius>
                <use_patch_radius>1</use_patch_radius>
                <ode>
                  <slip>0</slip>
                </ode>
              </torsional>
            </friction>
            <bounce>
              <restitution_coefficient>0</restitution_coefficient>
              <threshold>1e+06</threshold>
            </bounce>
            <contact>
              <collide_without_contact>0</collide_without_contact>
              <collide_without_contact_bitmask>1</collide_without_contact_bitmask>
              <collide_bitmask>1</collide_bitmask>
              <ode>
                <soft_cfm>0</soft_cfm>
                <soft_erp>0.2</soft_erp>
                <kp>1e+13</kp>
                <kd>1</kd>
                <max_vel>0.01</max_vel>
                <min_depth>0</min_depth>
              </ode>
              <bullet>
                <split_impulse>1</split_impulse>
                <split_impulse_penetration_threshold>-0.01</split_impulse_penetration_threshold>
                <soft_cfm>0</soft_cfm>
                <soft_erp>0.2</soft_erp>
                <kp>1e+13</kp>
                <kd>1</kd>
              </bullet>
            </contact>
          </surface>
        </collision>
      </link>
      <static>0</static>
      <allow_auto_disable>1</allow_auto_disable>
      <pose>0 0 0 0 -0 0</pose>
    </model>
    <model name='unit_box_1'>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
        <pose>0 0 0.31 0 -0 0</pose>
        <gravity>1</gravity>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.6 0.6 0.6</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
            <shader type='pixel'>
              <normal_map>__default__</normal_map>
            </shader>
            <ambient>0.3 0.02 0.2 1</ambient>
            <diffuse>0 0 0.2 1</diffuse>
            <specular>0 0 0 1</specular>
            <emissive>0 0 0.2 1</emissive>
            <lighting>1</lighting>
          </material>
          <pose>0 0 0 0 -0 0</pose>
          <transparency>0</transparency>
          <cast_shadows>1</cast_shadows>
        </visual>
        <collision name='collision'>
          <laser_retro>0</laser_retro>
          <max_contacts>10</max_contacts>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.6 0.6 0.6</size>
            </box>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>1</mu>
                <mu2>1</mu2>
                <fdir1>0 0 0</fdir1>
                <slip1>0</slip1>
                <slip2>0</slip2>
              </ode>
              <torsional>
                <coefficient>1</coefficient>
                <patch_radius>0</patch_radius>
                <surface_radius>0</surface_radius>
                <use_patch_radius>1</use_patch_radius>
                <ode>
                  <slip>0</slip>
                </ode>
              </torsional>
            </friction>
            <bounce>
              <restitution_coefficient>0</restitution_coefficient>
              <threshold>1e+06</threshold>
            </bounce>
            <contact>
              <collide_without_contact>0</collide_without_contact>
              <collide_without_contact_bitmask>1</collide_without_contact_bitmask>
              <collide_bitmask>1</collide_bitmask>
              <ode>
                <soft_cfm>0</soft_cfm>
                <soft_erp>0.2</soft_erp>
                <kp>1e+13</kp>
                <kd>1</kd>
                <max_vel>0.01</max_vel>
                <min_depth>0</min_depth>
              </ode>
              <bullet>
                <split_impulse>1</split_impulse>
                <split_impulse_penetration_threshold>-0.01</split_impulse_penetration_threshold>
                <soft_cfm>0</soft_cfm>
                <soft_erp>0.2</soft_erp>
                <kp>1e+13</kp>
                <kd>1</kd>
              </bullet>
            </contact>
          </surface>
        </collision>
      </link>
      <static>0</static>
      <allow_auto_disable>1</allow_auto_disable>
      <pose>1.75 -2 0 0 -0 0</pose>
    </model>
    <model name='unit_box_0'>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
        <pose>0 0 0.31 0 -0 0</pose>
        <gravity>1</gravity>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.6 0.6 0.6</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
            <shader type='pixel'>
              <normal_map>__default__</normal_map>
            </shader>
            <ambient>0.3 0.02 0 1</ambient>
            <diffuse>0 0.2 0 1</diffuse>
            <specular>0.01 0.01 0.01 1</specular>
            <emissive>0 0.2 0 1</emissive>
            <lighting>1</lighting>
          </material>
          <pose>0 0 0 0 -0 0</pose>
          <transparency>0</transparency>
          <cast_shadows>1</cast_shadows>
        </visual>
        <collision name='collision'>
          <laser_retro>0</laser_retro>
          <max_contacts>10</max_contacts>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.6 0.6 0.6</size>
            </box>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>1</mu>
                <mu2>1</mu2>
                <fdir1>0 0 0</fdir1>
                <slip1>0</slip1>
                <slip2>0</slip2>
              </ode>
              <torsional>
                <coefficient>1</coefficient>
                <patch_radius>0</patch_radius>
                <surface_radius>0</surface_radius>
                <use_patch_radius>1</use_patch_radius>
                <ode>
                  <slip>0</slip>
                </ode>
              </torsional>
            </friction>
            <bounce>
              <restitution_coefficient>0</restitution_coefficient>
              <threshold>1e+06</threshold>
            </bounce>
            <contact>
              <collide_without_contact>0</collide_without_contact>
              <collide_without_contact_bitmask>1</collide_without_contact_bitmask>
              <collide_bitmask>1</collide_bitmask>
              <ode>
                <soft_cfm>0</soft_cfm>
                <soft_erp>0.2</soft_erp>
                <kp>1e+13</kp>
                <kd>1</kd>
                <max_vel>0.01</max_vel>
                <min_depth>0</min_depth>
              </ode>
              <bullet>
                <split_impulse>1</split_impulse>
                <split_impulse_penetration_threshold>-0.01</split_impulse_penetration_threshold>
                <soft_cfm>0</soft_cfm>
                <soft_erp>0.2</soft_erp>
                <kp>1e+13</kp>
                <kd>1</kd>
              </bullet>
            </contact>
          </surface>
        </collision>
      </link>
      <static>0</static>
      <allow_auto_disable>1</allow_auto_disable>
      <pose>0.75 2 0 0 -0 0</pose>
    </model>
  </world>
</sdf>
