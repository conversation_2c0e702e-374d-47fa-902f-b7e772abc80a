<?xml version='1.0'?>
<sdf version='1.6'>
  <model name='drawer_white'>
    <link name='link_0'>
      <pose frame=''>-0.004287 -0.020202 0 0 -0 0</pose>
       <visual name='visual'>
        <geometry>
          <mesh>
            <uri>model://drawer_white/meshes/base.dae</uri>
            <scale>1 1 1</scale>
          </mesh>
        </geometry>
         <material>
          <script>
            <uri>model://drawer_white/materials/scripts</uri>
            <uri>model://drawer_white/materials/textures</uri>
            <name>drawer_white</name>
          </script>
        </material>
        <cast_shadows>1</cast_shadows>
        <transparency>0</transparency>
      </visual>
      <collision name='collision'>
        <laser_retro>0</laser_retro>
        <max_contacts>10</max_contacts>
        <pose frame=''>0 0 0 0 -0 0</pose>
        <geometry>
          <mesh>
            <uri>model://drawer_white/meshes/base.dae</uri>
            <scale>1 1 1</scale>
          </mesh>
        </geometry>
      </collision>
    </link>
    <link name='link_1'>
      <pose frame=''>0.004286 0.020202 0 0 -0 0</pose>
        <visual name='visual'>
        <pose frame=''>0 0 0 0 -0 0</pose>
        <geometry>
          <mesh>
            <uri>model://drawer_white/meshes/handle.dae</uri>
            <scale>1 1 1</scale>
          </mesh>
        </geometry>
        <material>
          <lighting>1</lighting>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Grey</name>
          </script>
          <ambient>0.3 0.3 0.3 1</ambient>
          <diffuse>0.7 0.7 0.7 1</diffuse>
          <specular>0.01 0.01 0.01 1</specular>
          <emissive>0 0 0 1</emissive>
        </material>
        <cast_shadows>1</cast_shadows>
        <transparency>0</transparency>
      </visual>
      <collision name='collision'>
        <laser_retro>0</laser_retro>
        <max_contacts>10</max_contacts>
        <pose frame=''>0 0 0 0 -0 0</pose>
        <geometry>
          <mesh>
            <uri>model://drawer_white/meshes/handle.dae</uri>
            <scale>1 1 1</scale>
          </mesh>
        </geometry>
       </collision>
    </link>
    <static>1</static>
    <allow_auto_disable>1</allow_auto_disable>
  </model>
</sdf>
