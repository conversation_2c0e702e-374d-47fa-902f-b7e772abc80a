import rospy
import pickle
from geometry_msgs.msg import PoseArray
from geometry_msgs.msg import TransformStamped

all_pts = []
vicon_pos = []



def control_fun(velx, vely, rotation):
    pass


def vicon_callback1(msg):
    # vicon_pos.append([msg.transform.translation.x, msg.transform.translation.y, msg.transform.translation.z])
    # print(len(vicon_pos))
    # if len(vicon_pos) == 2930:
    #    with open("vicon.pts", "wb") as f:
    #        pickle.dump(vicon_pos, f)
    '''do what you want with vicon msg'''
    swarm1_position_x =  msg.transform.translation.x # the position of swarm1 
    swarm1_position_y =  msg.transform.translation.y
    swarm1_position_z =  msg.transform.translation.z

    swarm1_oritention_x = msg.transform.rotation.x # the oritention of swarm1
    swarm1_oritention_y = msg.transform.rotation.y
    swarm1_oritention_z = msg.transform.rotation.z
    swarm1_oritention_w = msg.transform.rotation.w
   
    # an example of the speed control
    if swarm1_position_x == 1:
        control_fun(1.0, 1.0, 90)

def vicon_callback2(msg):
    ''' same as vicon_callback1'''
    pass

def vicon_callback_old(msg):
    '''same as vicon_callback1'''
    pass

def yolo_callback(msg):
    pass

    

if __name__ == '__main__':

    rospy.init_node('test_demo',anonymous=True)
    rospy.Subscriber('/yolo_result',PoseArray, yolo_callback)
    rospy.Subscriber('/vicon/VSWARM1/VSWARM1',TransformStamped, vicon_callback1)
    rospy.Subscriber('/vicon/VSWARM2/VSWARM2',TransformStamped, vicon_callback2)
    rospy.Subscriber('/vicon/OLD_TARGET/OLD_TARGET',TransformStamped, vicon_callback_old)

    rospy.spin()

