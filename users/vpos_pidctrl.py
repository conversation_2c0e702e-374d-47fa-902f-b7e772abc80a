import rospy
import socket
import threading
from geometry_msgs.msg import Twist
from geometry_msgs.msg import TransformStamped
client_id = f'{socket.gethostname()}' 
hostname = f"/{client_id}" 
global error_x,error_y

class pid_controller:
    # 给pid的三个参数赋初值
    def __init__(self, kp, ki, kd, output_limit, integral_limit = 1, derivative_limit = 0.4):
        self.kp = kp
        self.ki = ki
        self.kd = kd
        self.output_limit = output_limit
        self.integral_limit = integral_limit
        self.derivative_limit = derivative_limit
        
        self.last_error = 0.0
        self.integral = 0.0
        
    def limit(self, value, lower, upper):
        if value < lower:
            return lower
        elif value > upper:
            return upper
        else:
            return value
    def change_para(self, kp, ki, kd):
        self.kp = kp
        self.ki = ki
        self.kd = kd

    def control_action(self, error, dt):

        p = self.kp * error

        self.integral += error
        self.integral = self.limit(self.integral, -self.integral_limit, self.integral_limit)
        i = self.ki * self.integral

        self.derivative = (error - self.last_error) / dt
        self.derivative = self.limit(self.derivative, -self.derivative_limit, self.derivative_limit)
        d = self.kd * self.derivative
        self.last_error = error
        
        return self.limit((p + i + d), -self.output_limit, self.output_limit)

class ros_node_thread():
    def __init__(self):
        try:
            rospy.init_node('pos_ctrl', anonymous=True)
            rospy.Subscriber('/vicon'+ hostname + hostname,TransformStamped, self.handle_pos_data)
            print("Connected to ROS OK!")
        except:
            print("Connected to ROS Failed!")   
    def handle_pos_data(self, msg):
        global error_x, error_y
        error_x = 1.0 - msg.transform.translation.x
        error_y = 0.5 - msg.transform.translation.y
            
    def run(self):   
        while not rospy.is_shutdown():
            rospy.spin()
            
            
if __name__ =="__main__":
    error_x = 1
    error_y = 0
    ros_instance = ros_node_thread()
    ros_thread = threading.Thread(target=ros_instance.run)
    ros_thread.start()
    ros_hz = 10
    vel_pub = rospy.Publisher('robot/velcmd', Twist, queue_size=10)
    rate = rospy.Rate(ros_hz)
    pid_x= pid_controller(kp = 0.5, ki = 0.00000, kd = 0.001, output_limit=1)
    pid_y= pid_controller(kp = 0.5, ki = 0.00000, kd = 0.001, output_limit=1)
    while not rospy.is_shutdown(): 
        vel3 = Twist()
        print(error_x)
        print(error_y)
        vel3.linear.x = -pid_x.control_action(error = error_x, dt = (1/ros_hz)) 
        vel3.linear.y = -pid_y.control_action(error = error_y, dt = (1/ros_hz)) 
        vel3.angular.z = 0
        vel_pub.publish(vel3)
        rate.sleep()

    
