import rospy
import socket
import threading
from geometry_msgs.msg import Twist
from geometry_msgs.msg import TransformStamped
from idPD import idPD
#client_id = f'{socket.gethostname()}' 
#hostname = f"/{client_id}" 


class ros_node_thread():
    def __init__(self):
        try:
            rospy.init_node('pos_ctrl', anonymous=True)
            # rospy.Subscriber('/vicon'+ hostname + hostname,TransformStamped, self.handle_pos_data)
            rospy.Subscriber('/detect_result_pub/cam2',TransformStamped, self.handle_pos_data2)
            rospy.Subscriber('/detect_result_pub/cam1',TransformStamped, self.handle_pos_data1)
            self.vel_pub = rospy.Publisher('robot/velcmd', Twist, queue_size=10)
            print("Connected to ROS OK!")
        except:
            print("Connected to ROS Failed!")
        
        
        self.x1_goal = 90
        self.y1_goal = 180
        
        self.x2_goal = 90
        self.y2_goal = 450
        
        
        self.x1_diff = 0
        self.y1_diff = 0
        
        self.x2_diff = 0
        self.y2_diff = 0
        
        self.alpha = 1.0
        
        self.x_ctrl=idPD(P=4.5, D=1.4, scal=0.15*self.alpha, alpha=0.1, thres=0.9)
        self.y_ctrl=idPD(P=4.5, D=1.4, scal=0.15*self.alpha, alpha=0.1, thres=0.9)
        
    def handle_pos_data1(self, msg):
        
        x1_real = msg.transform.rotation.w #height
        y1_real = msg.transform.rotation.x #dir 
        
        self.x1_diff = x1_real - self.x1_goal
        self.y1_diff = y1_real - self.y1_goal
        
        sp_x = -self.x_ctrl.ctrl( (0.1*self.x1_diff + 1.9*self.x2_diff) /200.0)
        sp_y = -self.y_ctrl.ctrl( (0.1*self.y1_diff + 1.9*self.y2_diff) /200.0)
        #print(f'x: ***{x_real}, {sp_x}')
        print(f'y:@@@{y1_real}, {sp_y}')
        motion_cmd = Twist()
        motion_cmd.linear.x = sp_x
        motion_cmd.linear.y = sp_y
        motion_cmd.angular.z = 0
        self.vel_pub.publish(motion_cmd)
        
    def handle_pos_data2(self, msg):
        
        x2_real = msg.transform.rotation.w #height
        y2_real = msg.transform.rotation.x #dir 
        
        self.x2_diff = x2_real - self.x2_goal
        self.y2_diff = y2_real - self.y2_goal
        
        sp_x = -self.x_ctrl.ctrl( (0.1*self.x1_diff + 1.9*self.x2_diff) /200.0)
        sp_y = -self.y_ctrl.ctrl( (0.1*self.y1_diff + 1.9*self.y2_diff) /200.0)
        #print(f'x: ***{x_real}, {sp_x}')
        print(f'y:***{y2_real}, {sp_y}')
        motion_cmd = Twist()
        motion_cmd.linear.x = sp_x
        motion_cmd.linear.y = sp_y
        motion_cmd.angular.z = 0
        self.vel_pub.publish(motion_cmd)
            
    def run(self):   
        while not rospy.is_shutdown():
            rospy.spin()
            
            
if __name__ =="__main__":
    error_x = 0
    error_y = 0
    ros_instance = ros_node_thread()
    ros_thread = threading.Thread(target=ros_instance.run)
    ros_thread.start()
    ros_hz = 100
    rate = rospy.Rate(ros_hz)

    while not rospy.is_shutdown(): 
        rate.sleep()

    
